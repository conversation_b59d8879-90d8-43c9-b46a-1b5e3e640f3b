<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Auth;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::AUTHENTICATION;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }
    protected function authenticated()
    {
        // Auth::logoutOtherDevices(request('password'));
        // to admin dashboard
        if (Auth::user()->isAdmin()) {
            return redirect(route('index'));
        }
        abort(404);
    }
    public function login(Request $request)
    {


        $validated = $request->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        if (Auth::attempt($validated)) {

            auth()->user()->generateCode();

            return redirect()->route('check2fa.index');
        }

        return redirect()
            ->route('login')
            ->with('error', 'You have entered invalid credentials');
    }
    public function username()
    {
        return 'username';
    }
}
