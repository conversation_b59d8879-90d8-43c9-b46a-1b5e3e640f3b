@extends('admin.layouts.app')

@section('title', 'اضافة مستخدم جديد')
@section('content')

<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">المعلومات العامة</h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="new-user-info">
                        <form action="{{route('admin.users.store')}}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="form-group col-md-6">
                                    <label class="form-label" for="fname">الاسم الكامل:</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        id="fname" name="name" value="{{old('name')}}">
                                    @error('name')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="form-label" for="mobno">رقم الهاتف:</label>
                                    <input type="text" class="form-control @error('mobile_number') is-invalid @enderror"
                                        id="mobno" name="mobile_number" value="{{old('mobile_number')}}">
                                    @error('mobile_number')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <hr>
                            <h5 class="mb-3">معلومات الدخول</h5>
                            <div class="row">
                                <div class="form-group col-md-6">
                                    <label class="form-label" for="uname">اسم المتسخدم:</label>
                                    <input type="text" class="form-control @error('username') is-invalid @enderror"
                                        id="uname" name="username" value="{{old('username')}}">
                                    @error('username')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="form-label" for="urule_name">الصلاحية:</label>
                                    <select class="form-control @error('rule_name') is-invalid @enderror"
                                        name="rule_name" id="urule_name">
                                        <option value="">اختر...</option>
                                        <option value="admin" {{old('rule')=='admin' ? 'selected' : '' }}>
                                            مسؤول النظام</option>
                                        <option value="user1" {{old('rule')=='user1' ? 'selected' : '' }}>
                                            الحسابات</option>
                                        <option value="user2" {{old('rule')=='user2' ? 'selected' : '' }}>
                                            المبيعات</option>
                                        <option value="user3" {{old('rule')=='user3' ? 'selected' : '' }}>
                                            القانونية</option>
                                        <option value="user4" {{old('rule')=='user4' ? 'selected' : '' }}>
                                            المراقب</option>
                                        <option value="user5" {{old('rule')=='user5' ? 'selected' : '' }}>
                                            حسابات ومبيعات</option>
                                        <option value="user6" {{old('rule')=='user6' ? 'selected' : '' }}>
                                            مشروع</option>
                                    </select>
                                    @error('rule_name')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="appendRules">
                                    @if (old('rule_name')=='user1')
                                    <div class="form-group col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" name="is_deposits" type="checkbox" value="1"
                                                {{old('is_deposits')=='1' ? 'checked' : '' }} id="defaultCheck1">
                                            <label class="form-check-label" for="defaultCheck1">
                                                دفتر الايداع
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" name="is_deposits_new" type="checkbox"
                                                value="1" {{old('is_deposits_new')=='1' ? 'checked' : '' }}
                                                id="defaultCheck2">
                                            <label class="form-check-label" for="defaultCheck2">
                                                ايداع جديد
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" name="is_expenses" type="checkbox" value="1"
                                                {{old('is_expenses')=='1' ? 'checked' : '' }} id="defaultCheck3">
                                            <label class="form-check-label" for="defaultCheck3">
                                                دفتر الصرف
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" name="is_expenses_new" type="checkbox"
                                                value="1" {{old('is_expenses_new')=='1' ? 'checked' : '' }}
                                                id="defaultCheck4">
                                            <label class="form-check-label" for="defaultCheck4">
                                                صرف جديد
                                            </label>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                                <div class="form-group col-md-6" style="position: relative;">
                                    <label class="form-label" for="pass">كلمة السر:</label>
                                    <input type="password"
                                        class="form-control @error('password') is-invalid @enderror password" id="pass"
                                        name="password" autocomplete="new-password">
                                    <span id="showPassword"
                                        style="position: absolute;left: 29px;top: 40px;cursor: pointer;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            fill="currentColor" class="bi bi-eye-fill" viewBox="0 0 16 16">
                                            <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0" />
                                            <path
                                                d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8m8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7" />
                                        </svg>
                                    </span>
                                    @error('password')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="form-group col-md-6">
                                    <label class="form-label" for="rpass">اعادة كتابة كلمة السر:</label>
                                    <input type="password"
                                        class="form-control @error('password_confirmation') is-invalid @enderror password"
                                        id="rpass" name="password_confirmation" autocomplete="new-password">
                                    @error('password_confirmation')
                                    <p class="text-danger">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">اضافة</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('admin.include.blog1')
@endsection