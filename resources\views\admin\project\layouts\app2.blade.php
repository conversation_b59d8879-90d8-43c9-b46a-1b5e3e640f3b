<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title')</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('public/assets/images/logo2.webp')}}">

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/core/libs.min.css')}}">

    <!-- Aos Animation Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/aos/dist/aos.css')}}">

    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/hope-ui.min.css?v=4.0.0')}}">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/custom.min.css?v=4.0.0')}}">

    <!-- Dark Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/dark.min.css')}}">

    <!-- Customizer Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/customizer.min.css')}}">

    <!-- RTL Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/rtl.min.css')}}">

    <!-- Toasty Notification Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/jquery-confirm-v3.3.4/jquery-confirm.min.css')}}">

    <!-- Select2 Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/select2/dist/css/select2.min.css')}}">

    <!-- Select2 Bootstrap Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/select2/dist/css/select2-bootstrap4.min.css')}}">

    <!-- Fontawesome -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/fontawesome-free-6.6.0/css/all.min.css')}}">

    <!-- Jquery Toast Plugin Master -->
    <link rel="stylesheet"
        href="{{ asset('public/assets/vendor/jquery-toast-plugin-master/dist/jquery.toast.min.css')}}">

    <!-- Style Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/style.css')}}">
    <style>
        :root {
            --bs-primary: #1c7b7c !important;
        }

        .iq-header-img {
            background-color: #091e3e !important;
        }

        .btn-custom {
            --bs-btn-color: #ffffff;
            --bs-btn-bg: #091e3e;
            --bs-btn-border-color: #0c2448;
            --bs-btn-hover-color: #ffffff;
            --bs-btn-hover-bg: #091e3e;
            --bs-btn-hover-border-color: #091e3e;
            --bs-btn-focus-shadow-rgb: 60, 174, 109;
            --bs-btn-active-color: #ffffff;
            --bs-btn-active-bg: #091e3e;
            --bs-btn-active-border-color: #091e3e;
            --bs-btn-active-shadow: 0 0px 0px rgba(0, 0, 0, 0);
            --bs-btn-disabled-color: #ffffff;
            --bs-btn-disabled-bg: #091e3e;
            --bs-btn-disabled-border-color: #091e3e;
        }

        .table-dark {
            --bs-table-color: #ffffff;
            --bs-table-bg: #1c7b7c;
            --bs-table-border-color: #1c7b7c;
            --bs-table-striped-bg: #1c7b7c;
            --bs-table-striped-color: #ffffff;
            --bs-table-active-bg: #1c7b7c;
            --bs-table-active-color: #ffffff;
            --bs-table-hover-bg: #1c7b7c;
            --bs-table-hover-color: #ffffff;
            color: var(--bs-table-color);
            border-color: var(--bs-table-border-color);
        }

        .credit-card-widget .card-header {
            background: -webkit-linear-gradient(225deg, #1c7b7c 6%, #1c7b7c 56%);
            background: -o-linear-gradient(225deg, var(--bs-primary) 6%, #1c7b7c 56%);
            background: linear-gradient(225deg, #1c7b7c 6%, #1c7b7c 56%);
        }
    </style>
</head>

<body class="  ">
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->
    <aside class="sidebar sidebar-default sidebar-white sidebar-base navs-rounded-all sidebar-mini">
        <div class="sidebar-header d-flex align-items-center justify-content-start">
            <a href="{{route('project.deposits.index')}}" class="navbar-brand">
                <!--Logo start-->
                <div class="logo-main">
                    <div class="logo-normal">
                        <img src="{{ asset('public/assets/images/logo2.webp') }}" alt="logo" width="30">
                    </div>
                    <div class="logo-mini">
                        <svg class="icon-30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2"
                                transform="rotate(-45 -0.757324 19.2427)" fill="currentColor" />
                            <rect x="7.72803" y="27.728" width="28" height="4" rx="2"
                                transform="rotate(-45 7.72803 27.728)" fill="currentColor" />
                            <rect x="10.5366" y="16.3945" width="16" height="4" rx="2"
                                transform="rotate(45 10.5366 16.3945)" fill="currentColor" />
                            <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2"
                                transform="rotate(45 10.5562 -0.556152)" fill="currentColor" />
                        </svg>
                    </div>
                </div>
                <!--logo End-->

                {{-- <h6 class="logo-title">مجمع النجوم السكني</h6> --}}
            </a>
            <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
                <i class="icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.25 12.2744L19.25 12.2744" stroke="currentColor" stroke-width="1.5"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M10.2998 18.2988L4.2498 12.2748L10.2998 6.24976" stroke="currentColor"
                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </i>
            </div>
        </div>
        <div class="sidebar-body pt-0 data-scrollbar">
            <div class="sidebar-list">
                <!-- Sidebar Menu Start -->
                <ul class="navbar-nav iq-main-menu" id="sidebar-menu">
                    <li class="nav-item static-item">
                        <a class="nav-link static-item disabled" href="#" tabindex="-1">
                            <span class="default-icon">القائمة الرئيسية</span>
                            <span class="mini-icon">-</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->route()->named('project.deposits.index') ? 'active' : '' }}"
                            aria-current="page"
                            href="{{\Auth::user()->rule == 'user6' ? route('project.deposits.index') : route('index')}}">
                            <i class="icon">
                                <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                    class="icon-20">
                                    <path opacity="0.4"
                                        d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z"
                                        fill="currentColor"></path>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2ZM4.53852 13.4655H7.92449C9.32676 13.4655 10.463 14.6114 10.463 16.0255V19.44C10.463 20.8532 9.32676 22 7.92449 22H4.53852C3.13626 22 2 20.8532 2 19.44V16.0255C2 14.6114 3.13626 13.4655 4.53852 13.4655ZM19.4615 13.4655H16.0755C14.6732 13.4655 13.537 14.6114 13.537 16.0255V19.44C13.537 20.8532 14.6732 22 16.0755 22H19.4615C20.8637 22 22 20.8532 22 19.44V16.0255C22 14.6114 20.8637 13.4655 19.4615 13.4655Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">الرئيسية</span>
                        </a>
                    </li>
                </ul>
                <!-- Sidebar Menu End -->
            </div>
        </div>
        <div class="sidebar-footer"></div>
    </aside>

    <main class="main-content">
        <div class="position-relative iq-banner">
            <!--Nav Start-->
            <nav class="nav navbar navbar-expand-lg navbar-light iq-navbar">
                <div class="container-fluid navbar-inner">
                    <a href="../dashboard/index.html" class="navbar-brand">
                        <!--Logo start-->
                        <div class="logo-main">
                            <div class="logo-normal">
                                <svg class="text-primary icon-30" viewBox="0 0 30 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2"
                                        transform="rotate(-45 -0.757324 19.2427)" fill="currentColor" />
                                    <rect x="7.72803" y="27.728" width="28" height="4" rx="2"
                                        transform="rotate(-45 7.72803 27.728)" fill="currentColor" />
                                    <rect x="10.5366" y="16.3945" width="16" height="4" rx="2"
                                        transform="rotate(45 10.5366 16.3945)" fill="currentColor" />
                                    <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2"
                                        transform="rotate(45 10.5562 -0.556152)" fill="currentColor" />
                                </svg>
                            </div>
                            <div class="logo-mini">
                                <svg class="text-primary icon-30" viewBox="0 0 30 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2"
                                        transform="rotate(-45 -0.757324 19.2427)" fill="currentColor" />
                                    <rect x="7.72803" y="27.728" width="28" height="4" rx="2"
                                        transform="rotate(-45 7.72803 27.728)" fill="currentColor" />
                                    <rect x="10.5366" y="16.3945" width="16" height="4" rx="2"
                                        transform="rotate(45 10.5366 16.3945)" fill="currentColor" />
                                    <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2"
                                        transform="rotate(45 10.5562 -0.556152)" fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <!--logo End-->

                        <h4 class="logo-title">مجمع النجوم السكني</h4>
                    </a>
                    <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
                        <i class="icon">
                            <svg width="20px" class="icon-20" viewBox="0 0 24 24">
                                <path fill="currentColor"
                                    d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z" />
                            </svg>
                        </i>
                    </div>
                    <div>
                        <h5>
                            نظام ادارة مجمع النجوم السكني
                        </h5>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon">
                            <span class="mt-2 navbar-toggler-bar bar1"></span>
                            <span class="navbar-toggler-bar bar2"></span>
                            <span class="navbar-toggler-bar bar3"></span>
                        </span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="mb-2 navbar-nav ms-auto align-items-center navbar-list mb-lg-0">
                            <li class="nav-item dropdown">
                                <a href="#" onclick="forceReload();" title="تحديث الصفحة CTRL + F5" class="nav-link"
                                    id="notification-drop" data-bs-toggle="dropdown">
                                    <i class="fa-solid fa-rotate"></i>
                                    <span class="bg-danger dots"></span>
                                </a>
                            </li>
                            @php
                            $notifaction =
                            count(Illuminate\Support\Facades\DB::table('installments')->where('price_date',
                            '<', date("Y-m-d"))->where('status',
                                '0')->get());
                                @endphp
                                <li class="nav-item dropdown">
                                    <a class="py-0 nav-link d-flex align-items-center" href="#" id="navbarDropdown"
                                        role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <img src="{{ asset('public/assets/images/avatars/01.png') }}" alt="User-Profile"
                                            class="theme-color-default-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_1.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-purple-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_2.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-blue-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_4.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-green-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_5.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-yellow-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_3.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-pink-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <div class="caption ms-3 d-none d-md-block">
                                            <h6 class="mb-0 caption-title">{{ Auth::user()->name }}</h6>
                                            <p class="mb-0 caption-sub-title">
                                                {{ Auth::user()->rule_name }}
                                            </p>
                                        </div>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                        <li
                                            style="{{ request()->route()->named('admin.profile') ? 'background: #3a57e8;' : '' }}">
                                            <a class="dropdown-item {{ request()->route()->named('admin.profile') ? 'text-white' : '' }}"
                                                href="{{route('admin.profile')}}">الملف
                                                الشخصي</a>
                                        </li>
                                        <li>
                                            <hr class="dropdown-divider" />
                                        </li>
                                        <li>

                                            <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                                      document.getElementById('logout-form').submit();">
                                                تسجيل الخروج
                                            </a>

                                            <form id="logout-form" action="{{ route('logout') }}" method="POST"
                                                class="d-none">
                                                @csrf
                                            </form>
                                        </li>
                                    </ul>
                                </li>
                        </ul>
                    </div>
                </div>
            </nav>
            <!-- Nav Header Component Start -->
            <div class="iq-navbar-header" style="height: 140px">
                <div class="container-fluid iq-container">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="flex-wrap d-flex justify-content-between align-items-center">
                                <div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="iq-header-img">

                </div>
            </div>
            <!-- Nav Header Component End -->
            <!--Nav End-->
        </div>
        <div class="conatiner-fluid content-inner mt-n5 py-0">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <div class="row row-cols-1">
                        <div class="overflow-hidden d-slider1 ">
                            <ul class="p-0 m-0 swiper-wrapper list-inline" style="margin-bottom: .1rem !important;">
                                @if (\Auth::user()->rule == 'admin' || \Auth::user()->rule == 'user6' ||
                                \Auth::user()->rule == 'user4')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="700"
                                    style="{{ request()->route()->named('project.deposits.show') ? 'background-color: #091e3e!important;' : '' }}">
                                    <a href="{{route('project.deposits.show')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/donation.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('project.deposits.show') ? 'color: #fff!important;' : '' }}">
                                                        دفتر الايداع</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @if (\Auth::user()->rule != 'user4')
                                <li class="swiper-slide card card-slide"
                                    style="{{ request()->route()->named('project.deposits.create') ? 'background-color: #091e3e!important;' : '' }}"
                                    data-aos="fade-up" data-aos-delay="800">
                                    <a href="{{route('project.deposits.create')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/wallet.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('project.deposits.create') ? 'color: #fff!important;' : '' }}">
                                                        ايداع جديد</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('project.expense.show') ? 'background-color: #091e3e!important;' : '' }}">
                                    <a href="{{route('project.expense.show')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/expense.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('project.expense.show') ? 'color: #fff!important;' : '' }}">
                                                        دفتر الصرف</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @if (\Auth::user()->rule != 'user4')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('project.expense.create') ? 'background-color: #091e3e!important;' : '' }}">
                                    <a href="{{route('project.expense.create')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/capital.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('project.expense.create') ? 'color: #fff!important;' : '' }}">
                                                        صرف جديد</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
                @yield('content')
            </div>
        </div>
        <!-- Footer Section Start -->
        <footer class="footer">
            <div class="footer-body">
                <div class="lefts-panel">
                    ©
                    <script>
                        document.write(new Date().getFullYear());
                    </script>
                    SEIO, Made with
                    <span class="">
                        <svg class="icon-15" width="15" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M15.85 2.50065C16.481 2.50065 17.111 2.58965 17.71 2.79065C21.401 3.99065 22.731 8.04065 21.62 11.5806C20.99 13.3896 19.96 15.0406 18.611 16.3896C16.68 18.2596 14.561 19.9196 12.28 21.3496L12.03 21.5006L11.77 21.3396C9.48102 19.9196 7.35002 18.2596 5.40102 16.3796C4.06102 15.0306 3.03002 13.3896 2.39002 11.5806C1.26002 8.04065 2.59002 3.99065 6.32102 2.76965C6.61102 2.66965 6.91002 2.59965 7.21002 2.56065H7.33002C7.61102 2.51965 7.89002 2.50065 8.17002 2.50065H8.28002C8.91002 2.51965 9.52002 2.62965 10.111 2.83065H10.17C10.21 2.84965 10.24 2.87065 10.26 2.88965C10.481 2.96065 10.69 3.04065 10.89 3.15065L11.27 3.32065C11.3618 3.36962 11.4649 3.44445 11.554 3.50912C11.6104 3.55009 11.6612 3.58699 11.7 3.61065C11.7163 3.62028 11.7329 3.62996 11.7496 3.63972C11.8354 3.68977 11.9247 3.74191 12 3.79965C13.111 2.95065 14.46 2.49065 15.85 2.50065ZM18.51 9.70065C18.92 9.68965 19.27 9.36065 19.3 8.93965V8.82065C19.33 7.41965 18.481 6.15065 17.19 5.66065C16.78 5.51965 16.33 5.74065 16.18 6.16065C16.04 6.58065 16.26 7.04065 16.68 7.18965C17.321 7.42965 17.75 8.06065 17.75 8.75965V8.79065C17.731 9.01965 17.8 9.24065 17.94 9.41065C18.08 9.58065 18.29 9.67965 18.51 9.70065Z"
                                fill="currentColor"></path>
                        </svg>
                    </span>
                    by <a href="https://seio.uk/" target="_blank">Scientific Engineering Information Office</a>
                </div>
            </div>
        </footer>
        <!-- Footer Section End -->
    </main>

    @include('admin.include.modal')
    <!-- Wrapper End-->
    <!-- Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/libs.min.js')}}"></script>

    <!-- External Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/external.min.js')}}"></script>

    <!-- Widgetchart Script -->
    <script src="{{ asset('public/assets/js/charts/widgetcharts.js')}}"></script>

    <!-- mapchart Script -->
    <script src="{{ asset('public/assets/js/charts/vectore-chart.js')}}"></script>
    <script src="{{ asset('public/assets/js/charts/dashboard.js')}}"></script>

    <!-- fslightbox Script -->
    <script src="{{ asset('public/assets/js/plugins/fslightbox.js')}}"></script>

    <!-- Settings Script -->
    <script src="{{ asset('public/assets/js/plugins/setting.js')}}"></script>

    <!-- Slider-tab Script -->
    <script src="{{ asset('public/assets/js/plugins/slider-tabs.js')}}"></script>

    <!-- Form Wizard Script -->
    <script src="{{ asset('public/assets/js/plugins/form-wizard.js')}}"></script>

    <!-- AOS Animation Plugin-->
    <script src="{{ asset('public/assets/vendor/aos/dist/aos.js')}}"></script>

    <!-- Toasty Notification Js -->
    <script src="{{ asset('public/assets/vendor/jquery-confirm-v3.3.4/jquery-confirm.min.js')}}"></script>

    <!-- App Script -->
    <script src="{{ asset('public/assets/js/hope-ui.js')}}" defer></script>

    <!-- Jquery Toast Plugin Master -->
    <script src="{{ asset('public/assets/vendor/jquery-toast-plugin-master/dist/jquery.toast.min.js')}}" defer></script>

    <!-- number2string Script -->
    <script src="{{ asset('public/assets/js/number2string.js')}}"></script>

    <!-- Fontawesome -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/fontawesome-free-6.6.0/js/all.min.js')}}">

    <!-- Select2 -->
    <script src="{{ asset('public/assets/vendor/select2/dist/js/select2.full.min.js')}}"></script>
    @if (request()->route()->named('project.deposits.show'))
    <script>
        function load_data(
        page,
        query = "",
        query2 = "",
        query3 = "",
        query4 = "",
    ) {
        $.ajax({
            url: $("#search_form").attr("data-url-show") + "?page=" + page,
            method: "GET",
            dataType: "json",
            data: {
                page: page,
                query: query,
                query2: query2,
                query3: query3,
                query4: query4,
            },
            success: function (data) {
                $("#dynamic_table").html(data.table_data);
                $("#sumAmount").text(data.sum);
            },
            error: function (data) {
                console.log(data);
            },
        });
    }
    // Search Ajax
    load_data(1);

    $(document).on("click", ".pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1],
            query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val();
        load_data(page, query, query2, query3, query4);
    });

    $("#search_form").bind("keyup change", function () {
        var query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val();

        load_data(1, query, query2, query3, query4);
    });
    </script>
    @elseif (request()->route()->named('project.expense.show'))
    <script>
        function load_data(
        page,
        query = "",
        query2 = "",
        query3 = "",
        query4 = "",
        query5 = "",
    ) {
        $.ajax({
            url: $("#search_form").attr("data-url-show") + "?page=" + page,
            method: "GET",
            dataType: "json",
            data: {
                page: page,
                query: query,
                query2: query2,
                query3: query3,
                query4: query4,
                query5: query5,
            },
            success: function (data) {
                $("#dynamic_table").html(data.table_data);
                $("#sumAmount").text(data.sum);
            },
            error: function (data) {
                console.log(data);
            },
        });
    }
    // Search Ajax
    load_data(1);

    $(document).on("click", ".pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1],
            query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val(),
            query5 = $("#search5").val();
        load_data(page, query, query2, query3, query4, query5);
    });

    $("#search_form").bind("keyup change", function () {
        var query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val(),
            query5 = $("#search5").val();

        load_data(1, query, query2, query3, query4, query5);
    });

    </script>
    @endif
    <!-- Style Script -->
    <script src="{{ asset('public/assets/js/project.js')}}"></script>
</body>

</html>