<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallets_actions', function (Blueprint $table) {
            $table->id();
            $table->integer('wallets_id')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('deposits_id')->nullable();
            $table->string('action')->nullable();
            $table->string('message')->nullable();
            $table->string('price_old')->nullable();
            $table->string('price_new')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallets_actions');
    }
};
