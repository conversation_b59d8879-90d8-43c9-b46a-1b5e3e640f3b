<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4' || \Auth::user()->rule == 'user6') {
            abort(404);
        }
        if (\Auth::user()->is_expenses == 0) {
            abort(404);
        }
        return view('admin.newexpense');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4') {
            abort(404);
        }
        if (\Auth::user()->is_expenses_new == 0) {
            abort(404);
        }
        $id = \Auth::user()->id;
        if ($request->ajax()) {
            $validated = $request->validate(
                [
                    'day'                   => 'required',
                    'month'                 => 'required',
                    'year'                  => 'required',
                    'expense_door'          => 'required',
                    'expense_amount'        => 'required',
                    'expense_details'       => 'required',
                    // 'expense_file'          => 'file|mimes:jpg,jpeg,png,pdf,doc,docx,xlsx|max:10240',
                ],
                [
                    'day.required'                              => 'يرجى اختيار اليوم',
                    'month.required'                            => 'يرجى اختيار الشهر',
                    'year.required'                             => 'يرجى اختيار السنة',
                    'expense_door.required'                     => 'يرجى اختيار باب الصرف',
                    'expense_amount.required'                   => 'يرجى ادخال مبلغ الصرف',
                    'expense_details.required'                  => 'يرجى كتابة التفاصيل',
                    // 'expense_file.file'                         => 'يرجى اختيار ملف',
                    // 'expense_file.mimes'                        => 'يرجى اختيار امتداد مسموح به (jpg,jpeg,png,pdf,doc,docx,xlsx)',
                    // 'expense_file.max'                          => 'الحد الاقصى للرفع هو 10MB',
                ]
            );
            if ($request->hasFile('expense_file')) {
                $allfiles = array();
                $files = $request->file('expense_file');

                foreach ($files as $key => $file) {
                    if ($file->isValid()) {
                        $filePath = $file->store('uploads/expense', 'public');
                        $fileName = $filePath;
                        $allfiles[] = $fileName;
                    }
                }
                $empty_array = array($allfiles);
                if (!empty($empty_array)) {
                    $res_file = implode(',', $allfiles);
                } else {
                    $res_file = NULL;
                }
            } else {
                $res_file = NULL;
            }

            $expense = Expense::create([
                'day'                       => $request->day,
                'month'                     => $request->month,
                'year'                      => $request->year,
                'expense_date'              => $request->year . '-' . $request->month . '-' . $request->day,
                'expense_door'              => $request->expense_door,
                'expense_door_name'         => $request->expense_door_name,
                'expense_name'              => $request->expense_name,
                'expense_amount'            => $request->expense_amount,
                'expense_details'           => $request->expense_details,
                'expense_file'              => $res_file,
                'user_id'                   => $id,
            ]);
            DB::insert('insert into logs (user_id, expense_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $expense->id, 'اضافة', 'تم اضافة سند الصرف رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);

            return response()->json(['state' => 'success']);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4') {
            abort(404);
        }
        if (\Auth::user()->is_expenses_new == 0) {
            abort(404);
        }
        $id = \Auth::user()->id;
        if ($request->ajax()) {
            $validated = $request->validate(
                [
                    'day'                   => 'required',
                    'month'                 => 'required',
                    'year'                  => 'required',
                    'expense_door'          => 'required',
                    'expense_amount'        => 'required',
                    'expense_details'       => 'required',
                    // 'expense_file'          => 'file|mimes:jpg,jpeg,png,pdf,doc,docx,xlsx|max:10240',
                ],
                [
                    'day.required'                              => 'يرجى اختيار اليوم',
                    'month.required'                            => 'يرجى اختيار الشهر',
                    'year.required'                             => 'يرجى اختيار السنة',
                    'expense_door.required'                     => 'يرجى اختيار باب الصرف',
                    'expense_amount.required'                   => 'يرجى ادخال مبلغ الصرف',
                    'expense_details.required'                  => 'يرجى كتابة التفاصيل',
                    // 'expense_file.file'                         => 'يرجى اختيار ملف',
                    // 'expense_file.mimes'                        => 'يرجى اختيار امتداد مسموح به (jpg,jpeg,png,pdf,doc,docx,xlsx)',
                    // 'expense_file.max'                          => 'الحد الاقصى للرفع هو 10MB',
                ]
            );
            if ($request->hasFile('expense_file')) {
                $allfiles = array();
                $files = $request->file('expense_file');

                foreach ($files as $key => $file) {
                    if ($file->isValid()) {
                        $filePath = $file->store('uploads/expense', 'public');
                        $fileName = $filePath;
                        $allfiles[] = $fileName;
                    }
                }
                $empty_array = array($allfiles);
                if (!empty($empty_array)) {
                    $res_file = implode(',', $allfiles);
                } else {
                    $res_file = $request->expense_file_old;
                }
            } else {
                $res_file = $request->expense_file_old;
            }

            Expense::find($request->id)->update([
                'day'                       => $request->day,
                'month'                     => $request->month,
                'year'                      => $request->year,
                'expense_date'              => $request->year . '-' . $request->month . '-' . $request->day,
                'expense_door'              => $request->expense_door,
                'expense_name'              => $request->expense_name,
                'expense_amount'            => $request->expense_amount,
                'expense_details'           => $request->expense_details,
                'expense_file'              => $res_file,
            ]);
            DB::insert('insert into logs (user_id, expense_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'تحديث', 'تم تحديث سند الصرف رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            return response()->json(['state' => 'success']);
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Expense  $expense
     * @return \Illuminate\Http\Response
     */
    public function show(Expense $expense, Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3') {
            abort(404);
        }
        if (\Auth::user()->is_expenses == 0) {
            abort(404);
        }
        if ($request->ajax()) {
            $output = '';
            $pagination = 10;

            $data = Expense::where('isDelete', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('expense_door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('expense_name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '') {
                        return $q->where('expense_details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') == '') {
                        return $q->where('expense_date', '=',  $request->get('query4'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') != '') {
                        return $q->where('expense_date', '>=', $request->get('query4'))
                            ->where('expense_date', '<=', $request->get('query5'));
                    }
                })
                ->orderBy('expense_date', 'desc');
            $data3 = $data->get();
            $data2 = $data->paginate($pagination);
            $total_row = $data2->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">التاريخ</th>
                                    <th scope="col">الباب</th>
                                    <th scope="col">المبلغ</th>
                                    <th scope="col">اسم الصرف</th>
                                    <th scope="col">التفاصيل</th>
                                    <th scope="col">الادارة</th>
                                </tr>
                            </thead><tbody>';
                foreach ($data3 as $key => $value) {
                    $sum += str_replace(',', '', $value->expense_amount);
                }
                foreach ($data2 as $row) {

                    $output .= '<tr>
                                    <td>' . $row->id . '</td>
                                    <td>' . $row->year . '/' . $row->month . '/' . $row->day . '</td>
                                    <td>' . DB::table('doors')->where('id', $row->expense_door)->first()->name . '</td>
                                    <td>' . $row->expense_amount . '</td>
                                    <td>' . $row->expense_name . '</td>
                                    <td>' . $row->expense_details . '</td>
                                    <td>
                                       <button type="button" class="me-2 btn ' . (!empty($row->expense_file) ? 'btn-info showfiles' : 'btn-light') . ' btn-sm" id="' . $row->id . '" data-url="' . url('') . '" data-get-files="' . $row->expense_file . '"
                                        title="الملفات"><i
                                                class="fa-solid fa-file"></i></button>';
                    if (\Auth::user()->rule == 'admin') {
                        $output .= '<button type="button" class="btn btn-primary btn-sm edit editexpense" data-type-form="2" id="' . $row->id . '" 
                                        data-day="' . $row->day . '" 
                                        data-month="' . $row->month . '" 
                                        data-year="' . $row->year . '" 
                                        data-expense-door="' . $row->expense_door . '"
                                        data-expense-name="' . $row->expense_name . '"
                                        data-expense-amount="' . $row->expense_amount . '"
                                        data-expense-details="' . $row->expense_details . '"
                                        data-expense-file="' . $row->expense_file . '"
                                        ><i
                                                class="fa-solid fa-pen-to-square"></i></button>

                                        <button type="button" class="btn btn-danger btn-sm delete" data-url-delete="' . route('expense.destroy') . '" id="' . $row->id . '" title="حذف"><i
                                                class="fa-solid fa-trash"></i></button>';
                    }
                    if (\Auth::user()->rule == 'admin' || \Auth::user()->rule == 'user4') {
                        $output .= '<button type="button" class="btn btn-warning btn-sm showlogs ms-2" data-logs-type="3" data-url-logs="' . route('logs') . '" id="' . $row->id . '" title="سجلات"><i class="fa-solid fa-circle-question"></i></button>';
                    }
                    $output .= '</td>
                                </tr>';
                }
                $output .= '</tbody></table></div>';
                $output .= $data2->links();
            } else {
                $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
            }
            return response()->json([
                'table_data' => $output,
                'sum' => number_format($sum, 0)
            ]);
        }
        return view('admin.showexpense');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Expense  $expense
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Expense $expense)
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        if ($request->ajax()) {
            $expense = Expense::find($request->id);

            $expense->isDelete = 1;

            $expense->save();

            DB::insert('insert into logs (user_id, expense_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'حذف', 'تم حذف سند الصرف رقم الاي دي ' . $request->id . '  بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            return 'success';
        }
    }
    public function printreportdaily(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $sum = 0;
            $expense = Expense::where('isDelete', 0)->whereDate('created_at', '=', Carbon::today()->toDateString())->orderBy('id', 'desc')->latest()->get();
            $total_row = $expense->count();
            if ($total_row > 0) {
                $output .= '<p>التاريخ: ' . Carbon::today()->toDateString() . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">رقم السند</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">اسم الصرف</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($expense as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->expense_door_name . '</td>
                                <td>' . $row->expense_amount . '</td>
                                <td>' . $row->expense_name . '</td>
                                <td>' . $row->expense_details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->expense_amount != null ? $row->expense_amount : 0);
                }

                $output .= '<tr><td colspan="2">المجموع</td><td colspan="3">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة', 'تم طباعة سند الصرف بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
    public function printreport(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = Expense::where('isDelete', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('expense_door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('expense_name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '') {
                        return $q->where('expense_details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') == '') {
                        return $q->where('expense_date', '=',  $request->get('query4'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') != '') {
                        return $q->where('expense_date', '>=', $request->get('query4'))
                            ->where('expense_date', '<=', $request->get('query5'));
                    }
                })
                ->orderBy('id', 'desc')
                ->get();
            $total_row = $deposit->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<p>تم سحب هذا التقرير بتاريخ: ' . Carbon::today()->toDateString() . ' الساعة ' . date('h:i a') . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                            <tr>
                                <th scope="col" style="width: 56px;">رقم السند</th>
                                <th scope="col" style="width: 66px;">التاريخ</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">اسم الصرف</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->expense_date . '</td>
                                <td>' . $row->expense_door_name . '</td>
                                <td>' . $row->expense_amount . '</td>
                                <td>' . $row->expense_name . '</td>
                                <td>' . $row->expense_details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->expense_amount != null ? $row->expense_amount : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="4">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة حسب الفرز', 'تم طباعة سند الصرف بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
}
