/*!
 * Flat Design Interactions for مجمع النجوم السكني
 * Enhanced User Experience with Modern Animations
 */

document.addEventListener("DOMContentLoaded", function () {
    "use strict";

    // ===== تحسين تفاعلات الشريط الجانبي الحديث =====
    const sidebarLinks = document.querySelectorAll(".sidebar .nav-link");

    sidebarLinks.forEach((link) => {
        link.addEventListener("mouseenter", function () {
            // تأثير الحركة
            this.style.transform = "translateX(-5px)";

            // تأثير الأيقونة
            const icon = this.querySelector(".nav-icon i");
            if (icon) {
                icon.style.transform = "scale(1.2) rotate(5deg)";
            }

            // تأثير النص
            const text = this.querySelector(".nav-text");
            if (text) {
                text.style.fontWeight = "600";
            }
        });

        link.addEventListener("mouseleave", function () {
            if (!this.classList.contains("active")) {
                this.style.transform = "translateX(0)";

                // إعادة الأيقونة
                const icon = this.querySelector(".nav-icon i");
                if (icon) {
                    icon.style.transform = "scale(1) rotate(0deg)";
                }

                // إعادة النص
                const text = this.querySelector(".nav-text");
                if (text) {
                    text.style.fontWeight = "500";
                }
            }
        });

        // تأثير النقر
        link.addEventListener("click", function () {
            // تأثير النقر
            this.style.transform = "translateX(-3px) scale(0.98)";
            setTimeout(() => {
                if (this.classList.contains("active")) {
                    this.style.transform = "translateX(-3px) scale(1)";
                } else {
                    this.style.transform = "translateX(0) scale(1)";
                }
            }, 150);
        });
    });

    // ===== تحسين تفاعلات رأس الشريط الجانبي =====
    const logoCircle = document.querySelector(".logo-circle");
    if (logoCircle) {
        logoCircle.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.1) rotate(5deg)";
            this.style.boxShadow = "0 8px 25px rgba(255, 255, 255, 0.3)";
        });

        logoCircle.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1) rotate(0deg)";
            this.style.boxShadow = "";
        });
    }

    // ===== تحسين تفاعلات زر الإغلاق =====
    const toggleIcon = document.querySelector(".toggle-icon");
    if (toggleIcon) {
        toggleIcon.addEventListener("mouseenter", function () {
            const icon = this.querySelector("i");
            if (icon) {
                icon.style.transform = "rotate(90deg)";
            }
        });

        toggleIcon.addEventListener("mouseleave", function () {
            const icon = this.querySelector("i");
            if (icon) {
                icon.style.transform = "rotate(0deg)";
            }
        });
    }

    // ===== تحسين تفاعلات البطاقات السريعة =====
    const cardSlides = document.querySelectorAll(".card-slide");

    cardSlides.forEach((card, index) => {
        // إضافة تأخير للرسوم المتحركة
        card.style.animationDelay = `${index * 0.1}s`;

        card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-8px) scale(1.02)";
            this.style.boxShadow =
                "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)";

            // تحريك الأزرار داخل البطاقة
            const buttons = this.querySelectorAll(".btn");
            buttons.forEach((btn, btnIndex) => {
                setTimeout(() => {
                    btn.style.transform = "translateY(-2px)";
                }, btnIndex * 100);
            });
        });

        card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) scale(1)";
            this.style.boxShadow =
                "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)";

            // إعادة الأزرار لوضعها الطبيعي
            const buttons = this.querySelectorAll(".btn");
            buttons.forEach((btn) => {
                btn.style.transform = "translateY(0)";
            });
        });
    });

    // ===== تحسين تفاعلات أزرار البطاقات =====
    const cardButtons = document.querySelectorAll(".card-slide .btn");

    cardButtons.forEach((button) => {
        button.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-3px) scale(1.05)";
        });

        button.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) scale(1)";
        });

        button.addEventListener("click", function (e) {
            // منع انتشار الحدث للبطاقة الأب
            e.stopPropagation();

            // إضافة تأثير النقر
            this.style.transform = "translateY(0) scale(0.95)";
            setTimeout(() => {
                this.style.transform = "translateY(-2px) scale(1)";
            }, 150);
        });
    });

    // ===== تحسين تفاعلات الأزرار =====
    const buttons = document.querySelectorAll(".btn");

    buttons.forEach((button) => {
        button.addEventListener("click", function (e) {
            // إنشاء تأثير الموجة
            const ripple = document.createElement("span");
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + "px";
            ripple.style.left = x + "px";
            ripple.style.top = y + "px";
            ripple.classList.add("ripple-effect");

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // ===== تحسين تفاعلات الجداول =====
    const tableRows = document.querySelectorAll(".table-row-hover");

    tableRows.forEach((row) => {
        row.addEventListener("mouseenter", function () {
            this.style.backgroundColor = "rgba(37, 99, 235, 0.05)";
            this.style.transform = "translateY(-2px)";
        });

        row.addEventListener("mouseleave", function () {
            this.style.backgroundColor = "";
            this.style.transform = "translateY(0)";
        });
    });

    // ===== تحسين تفاعلات النماذج =====
    const formControls = document.querySelectorAll(
        ".form-control, .form-select"
    );

    formControls.forEach((control) => {
        control.addEventListener("focus", function () {
            this.parentElement.style.transform = "scale(1.02)";
        });

        control.addEventListener("blur", function () {
            this.parentElement.style.transform = "scale(1)";
        });
    });

    // ===== تحسين تفاعلات القوائم المنسدلة =====
    const dropdownToggles = document.querySelectorAll(
        '[data-bs-toggle="dropdown"]'
    );

    dropdownToggles.forEach((toggle) => {
        toggle.addEventListener("click", function () {
            const icon = this.querySelector(".fa-chevron-down");
            if (icon) {
                icon.style.transform =
                    icon.style.transform === "rotate(180deg)"
                        ? "rotate(0deg)"
                        : "rotate(180deg)";
            }
        });
    });

    // ===== تحسين تفاعلات الإشعارات =====
    const notificationBell = document.querySelector("#notification-drop");
    if (notificationBell) {
        notificationBell.addEventListener("mouseenter", function () {
            const icon = this.querySelector("svg");
            if (icon) {
                icon.style.animation = "pulse 1s infinite";
            }
        });

        notificationBell.addEventListener("mouseleave", function () {
            const icon = this.querySelector("svg");
            if (icon) {
                icon.style.animation = "";
            }
        });
    }

    // ===== تحسين تفاعلات الشارات =====
    const badges = document.querySelectorAll(".badge");

    badges.forEach((badge) => {
        badge.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.1)";
        });

        badge.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1)";
        });
    });

    // ===== تحسين تفاعلات الأيقونات =====
    const iconWrappers = document.querySelectorAll(".icon-wrapper");

    iconWrappers.forEach((wrapper) => {
        wrapper.addEventListener("mouseenter", function () {
            const icon = this.querySelector("i");
            if (icon) {
                icon.style.transform = "scale(1.2) rotate(5deg)";
            }
        });

        wrapper.addEventListener("mouseleave", function () {
            const icon = this.querySelector("i");
            if (icon) {
                icon.style.transform = "scale(1) rotate(0deg)";
            }
        });
    });

    // ===== تحسين تفاعلات البطاقات =====
    const cards = document.querySelectorAll(".card");

    cards.forEach((card) => {
        card.addEventListener("mouseenter", function () {
            if (!this.classList.contains("card-slide")) {
                this.style.transform = "translateY(-4px)";
                this.style.boxShadow =
                    "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)";
            }
        });

        card.addEventListener("mouseleave", function () {
            if (!this.classList.contains("card-slide")) {
                this.style.transform = "translateY(0)";
                this.style.boxShadow =
                    "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)";
            }
        });
    });

    // ===== تحسين الرسوم المتحركة عند التمرير =====
    const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add("fade-in-up");
            }
        });
    }, observerOptions);

    // مراقبة العناصر للرسوم المتحركة
    const animatedElements = document.querySelectorAll(
        ".card, .table, .swiper-slide"
    );
    animatedElements.forEach((el) => observer.observe(el));

    // ===== تحسين تفاعلات الشريط العلوي =====
    const navbar = document.querySelector(".navbar");
    let lastScrollTop = 0;

    window.addEventListener("scroll", function () {
        const scrollTop =
            window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // التمرير لأسفل
            navbar.style.transform = "translateY(-100%)";
        } else {
            // التمرير لأعلى
            navbar.style.transform = "translateY(0)";
        }

        lastScrollTop = scrollTop;
    });

    // ===== تحسين تفاعلات الشريط الجانبي المتجاوب =====
    const sidebarToggle = document.querySelector(".sidebar-toggle");
    const sidebar = document.querySelector(".sidebar");

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener("click", function () {
            sidebar.classList.toggle("show");

            // تحديث أيقونة التبديل
            const icon = this.querySelector("svg path");
            if (icon) {
                if (sidebar.classList.contains("show")) {
                    icon.setAttribute("d", "M18 6L6 18M6 6L18 18");
                } else {
                    icon.setAttribute("d", "M3 12H21M3 6H21M3 18H21");
                }
            }
        });
    }

    // ===== تحسين تفاعلات البحث =====
    const searchInputs = document.querySelectorAll(
        'input[type="search"], input[placeholder*="بحث"]'
    );

    searchInputs.forEach((input) => {
        input.addEventListener("focus", function () {
            this.parentElement.style.boxShadow =
                "0 0 0 3px rgba(37, 99, 235, 0.1)";
            this.parentElement.style.borderColor = "var(--bs-primary)";
        });

        input.addEventListener("blur", function () {
            this.parentElement.style.boxShadow = "";
            this.parentElement.style.borderColor = "";
        });
    });

    // ===== إضافة تأثيرات CSS للموجة =====
    const style = document.createElement("style");
    style.textContent = `
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    console.log("🎨 Flat Design Interactions Loaded Successfully!");
});
