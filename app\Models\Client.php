<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;


    protected $fillable = [
        'house_no',
        'house_space',
        'buld_space',
        'rooms_no',
        'totol_house_price',
        'name_purshes',
        'name_mother_purshes',
        'phone1',
        'phone2',
        'id_no',
        'id_relese',
        'id_relese_date',
        'address',
        'sold_type',
        'sold_price',
        'begen_price',
        'begen_date',
        'delvery_key_prise',
        'delvery_key_date',
        'months_no',
        'years_no',
        'monthly_price',
        'annual_price',
        'addprice',
        'prise_add',
        'prise_add2',
        'prise_add3',
        'prise_add4',
        'prise_add5',
        'prise_add6',
        'prise_add7',
        'prise_add8',
        'prise_add9',
        'prise_add10',
        'prise_add11',
        'prise_add12',
        'prise_add13',
        'prise_add14',
        'prise_add15',
        'prise_add16',
        'prise_add17',
        'prise_add18',
        'prise_add19',
        'prise_add20',
        'first_installment_date',
        'payments_no',
        'payment_price',
        'payment_date',
        'files',
        'user_id',
        'isDelete',
    ];
}
