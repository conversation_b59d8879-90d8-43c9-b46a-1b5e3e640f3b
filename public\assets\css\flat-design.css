/*!
 * Flat Design Enhancements for مجمع النجوم السكني
 * Modern Flat UI Components and Animations
 */

/* ===== متغيرات إضافية للتصميم الفلات ===== */
:root {
    --flat-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --flat-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --flat-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --flat-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --flat-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== تحسينات الرسوم المتحركة ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes pulse {
    0%,
    100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* ===== تحسينات البطاقات السريعة المحدثة ===== */
.swiper-slide {
    animation: fadeInUp 0.6s ease-out;
    min-height: 260px;
    display: flex;
    align-items: stretch;
    position: relative;
}

.swiper-slide::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: var(--bs-border-radius);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.swiper-slide:hover::after {
    opacity: 1;
}

.swiper-slide:nth-child(1) {
    animation-delay: 0.1s;
}
.swiper-slide:nth-child(2) {
    animation-delay: 0.2s;
}
.swiper-slide:nth-child(3) {
    animation-delay: 0.3s;
}
.swiper-slide:nth-child(4) {
    animation-delay: 0.4s;
}
.swiper-slide:nth-child(5) {
    animation-delay: 0.5s;
}
.swiper-slide:nth-child(6) {
    animation-delay: 0.6s;
}

.card-slide {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.card-slide .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.card-slide::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.card-slide:hover::before {
    left: 100%;
}

/* ===== تحسينات أزرار البطاقات ===== */
.card-slide .btn {
    font-size: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-slide .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-slide .btn-outline-success:hover {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
    color: white;
}

.card-slide .btn-outline-danger:hover {
    background-color: var(--bs-danger);
    border-color: var(--bs-danger);
    color: white;
}

.card-slide .btn-outline-info:hover {
    background-color: var(--bs-info);
    border-color: var(--bs-info);
    color: white;
}

.card-slide .btn-outline-primary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* تأثير الموجة للأزرار الصغيرة */
.card-slide .btn::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.card-slide .btn:active::after {
    width: 120px;
    height: 120px;
}

/* ===== تحسينات الشريط الجانبي ===== */
.sidebar {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.sidebar .nav-link {
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: "";
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(37, 99, 235, 0.1),
        transparent
    );
    transition: right 0.5s;
}

.sidebar .nav-link:hover::before {
    right: 100%;
}

.sidebar .nav-link.active {
    position: relative;
}

.sidebar .nav-link.active::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background-color: var(--bs-primary);
    border-radius: 2px;
}

/* ===== تحسينات شريط التنقل ===== */
.navbar {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-brand {
    animation: slideInLeft 0.8s ease-out;
}

.navbar .nav-item {
    animation: slideInRight 0.8s ease-out;
}

.navbar .nav-item:nth-child(1) {
    animation-delay: 0.1s;
}
.navbar .nav-item:nth-child(2) {
    animation-delay: 0.2s;
}
.navbar .nav-item:nth-child(3) {
    animation-delay: 0.3s;
}

/* ===== تحسينات الجداول ===== */
.table-row-hover {
    transition: var(--flat-transition);
}

.table-row-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--flat-shadow-md);
}

.table thead th {
    position: relative;
    background: linear-gradient(
        135deg,
        var(--bs-primary) 0%,
        var(--bs-primary-dark) 100%
    );
}

.table thead th::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--bs-primary-light),
        var(--bs-primary-dark)
    );
}

/* ===== تحسينات الأزرار ===== */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* ===== تحسينات النماذج ===== */
.form-control:focus {
    transform: translateY(-2px);
    box-shadow: var(--flat-shadow-lg);
}

.form-select:focus {
    transform: translateY(-2px);
    box-shadow: var(--flat-shadow-lg);
}

/* ===== تحسينات الشارات ===== */
.badge {
    position: relative;
    overflow: hidden;
    animation: bounceIn 0.6s ease-out;
}

.badge::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s;
}

.badge:hover::before {
    left: 100%;
}

/* ===== تحسينات القوائم المنسدلة ===== */
.dropdown-menu {
    animation: fadeInUp 0.3s ease-out;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.dropdown-item {
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: "";
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(37, 99, 235, 0.1),
        transparent
    );
    transition: right 0.3s;
}

.dropdown-item:hover::before {
    right: 100%;
}

/* ===== تحسينات الأيقونات ===== */
.icon-wrapper i {
    transition: var(--flat-transition);
}

.nav-link:hover .icon-wrapper i {
    transform: scale(1.1);
}

.icon-circle {
    transition: var(--flat-transition);
}

.card-slide:hover .icon-circle {
    transform: scale(1.1) rotate(5deg);
}

/* ===== تحسينات الاستجابة المحسنة ===== */
@media (max-width: 1200px) {
    .swiper-slide {
        width: 280px !important;
        height: 240px !important;
    }
}

@media (max-width: 768px) {
    .swiper-slide {
        width: 250px !important;
        height: 220px !important;
        margin: 0 10px !important;
    }

    .swiper-slide .card-body {
        padding: 20px !important;
    }

    .swiper-slide .icon-circle {
        width: 60px !important;
        height: 60px !important;
    }

    .swiper-slide .icon-circle i {
        font-size: 24px !important;
    }

    .swiper-slide .progress-detail h6 {
        font-size: 16px !important;
    }

    .table-responsive {
        font-size: 12px;
    }

    .navbar .brand-text {
        display: none;
    }

    .sidebar {
        width: 260px !important;
    }

    .sidebar-header {
        padding: 20px 15px !important;
    }
}

@media (max-width: 576px) {
    .swiper-slide {
        width: 220px !important;
        height: 200px !important;
        margin: 0 8px !important;
    }

    .swiper-slide .card-body {
        padding: 18px !important;
    }

    .swiper-slide .icon-circle {
        width: 50px !important;
        height: 50px !important;
    }

    .swiper-slide .icon-circle i {
        font-size: 20px !important;
    }

    .swiper-slide .progress-detail h6 {
        font-size: 14px !important;
    }

    .swiper-slide .progress-detail small {
        font-size: 11px !important;
    }

    .swiper-slide .btn-sm {
        font-size: 11px !important;
        padding: 8px 12px !important;
        min-height: 32px !important;
    }

    .sidebar {
        width: 240px !important;
    }

    .brand-title {
        font-size: 14px !important;
    }

    .brand-subtitle {
        font-size: 10px !important;
    }
}

/* ===== تأثيرات خاصة ===== */
.pulse-animation {
    animation: pulse 2s infinite;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

/* ===== تحسينات إمكانية الوصول ===== */
.nav-link:focus,
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

/* ===== تحسينات الطباعة المحسنة ===== */
@media print {
    .fade-in-up,
    .slide-in-right,
    .slide-in-left,
    .bounce-in {
        animation: none !important;
    }

    .card {
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }
}
