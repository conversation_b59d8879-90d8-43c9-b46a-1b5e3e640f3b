<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Project2Controller extends Controller
{
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (\Auth::user()->rule == 'admin' ||  \Auth::user()->rule == 'user6') {
            return view('admin.project.newexpense');
        } else {
            abort(404);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (\Auth::user()->rule != 'user4') {
            $id = \Auth::user()->id;
            if ($request->ajax()) {
                $validated = $request->validate(
                    [
                        'day'                   => 'required',
                        'month'                 => 'required',
                        'year'                  => 'required',
                        'expense_door'          => 'required',
                        'expense_name'          => 'required',
                        'currency'              => 'required',
                        'expense_amount'        => 'required',
                        'expense_details'       => 'required',
                    ],
                    [
                        'day.required'                              => 'يرجى اختيار اليوم',
                        'month.required'                            => 'يرجى اختيار الشهر',
                        'year.required'                             => 'يرجى اختيار السنة',
                        'expense_door.required'                     => 'يرجى اختيار باب الصرف',
                        'expense_name.required'                     => 'يرجى ادخال اسم الصرف',
                        'currency.required'                         => 'يرجى اختيار العملة',
                        'expense_amount.required'                   => 'يرجى ادخال مبلغ الصرف',
                        'expense_details.required'                  => 'يرجى كتابة التفاصيل',
                    ]
                );


                if ($request->hasFile('expense_file')) {
                    $allfiles = array();
                    $files = $request->file('expense_file');

                    foreach ($files as $key => $file) {
                        if ($file->isValid()) {
                            $filePath = $file->store('uploads/expense', 'public');
                            $fileName = $filePath;
                            $allfiles[] = $fileName;
                        }
                    }
                    $empty_array = array($allfiles);
                    if (!empty($empty_array)) {
                        $res_file = implode(',', $allfiles);
                    } else {
                        $res_file = NULL;
                    }
                } else {
                    $res_file = NULL;
                }
                $expenseid = DB::table('project_deposits_expenses')->insertGetId([
                    'day'                       => $request->day,
                    'month'                     => $request->month,
                    'year'                      => $request->year,
                    'date'                      => $request->year . '-' . $request->month . '-' . $request->day,
                    'door'                      => $request->expense_door,
                    'name'                      => $request->expense_name,
                    'currency'                  => $request->currency,
                    'currency_price'            => $request->currency_price,
                    'price_dollar'              => $request->price_dollar,
                    'amount'                    => $request->expense_amount,
                    'details'                   => $request->expense_details,
                    'file'                      => $res_file,
                    'type'                      => 2,
                    'user_id'                   => $id,
                    'created_at'                => date('Y-m-d H:i:s'),
                    'updated_at'                => date('Y-m-d H:i:s'),
                ]);

                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $expenseid, 'اضافة مشروع', 'تم اضافة سند الصرف رقم الاي دي ' . $expenseid . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);

                return response()->json(['state' => 'success']);
            }
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */

    public function show(Request $request)
    {
        if (\Auth::user()->rule == 'admin' ||  \Auth::user()->rule == 'user6' ||  \Auth::user()->rule == 'user4') {

            if ($request->ajax()) {
                $output = '';
                $pagination = 10;

                $data = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', '2')
                    ->where(function ($q) use ($request) {
                        if ($request->get('query') != '') {
                            return $q->where('door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query2') != '') {
                            return $q->where('name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query3') != '') {
                            return $q->where('details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query4') != '' && $request->get('query5') == '') {
                            return $q->where('date', '=',  $request->get('query4'));
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query4') != '' && $request->get('query5') != '') {
                            return $q->where('date', '>=', $request->get('query4'))
                                ->where('date', '<=', $request->get('query5'));
                        }
                    })
                    ->orderBy('date', 'desc');
                $data3 = $data->get();
                $data2 = $data->paginate($pagination);
                $total_row = $data2->count();
                $sum = 0;
                if ($total_row > 0) {
                    $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                             <thead class="table-dark">
                                 <tr>
                                     <th scope="col">#</th>
                                     <th scope="col">التاريخ</th>
                                     <th scope="col">المبلغ</th>
                                     <th scope="col">العملة</th>
                                     <th scope="col">سعر الصرف</th>
                                     <th scope="col">المبلغ بالدولار</th>
                                     <th scope="col">الباب</th>
                                     <th scope="col">اسم الصرف</th>
                                     <th scope="col">التفاصيل</th>
                                     <th scope="col">الادارة</th>
                                 </tr>
                             </thead><tbody>';
                    foreach ($data3 as $key => $value) {
                        $sum += str_replace(',', '', $value->amount);
                    }
                    foreach ($data2 as $row) {
                        $output .= '<tr>
                                     <td>' . $row->id . '</td>
                                     <td>' . $row->date . '</td>
                                     <td>' . $row->amount . '</td>
                                     <td>' . $row->currency . '</td>
                                     <td>' . $row->currency_price . '</td>
                                     <td>' . $row->price_dollar . '</td>
                                     <td>' . DB::table('doors')->select('name')->where('id', $row->door)->first()->name . '</td>
                                     <td>' . $row->name . '</td>
                                     <td>' . $row->details . '</td>
                                     <td>
                                         <button type="button" class="me-2 btn ' . (!empty($row->file) ? 'btn-info showfiles' : 'btn-light') . ' btn-sm" id="' . $row->id . '" data-url="' . url('') . '" data-get-files="' . $row->file . '"
                                          title="الملفات"><i class="fa-solid fa-file"></i></button>';
                        if (\Auth::user()->rule != 'user4') {
                            $output .= '<button type="button" class="btn btn-primary edit btn-sm" data-type-form="2" id="' . $row->id . '"
                                        data-day="' . $row->day . '" 
                                        data-month="' . $row->month . '" 
                                        data-year="' . $row->year . '" 
                                        data-expense-door="' . $row->door . '"
                                        data-expense-name="' . $row->name . '"
                                        data-expense-amount="' . $row->amount . '"
                                        data-expense-details="' . $row->details . '"
                                        data-expense-file="' . $row->file . '"
                        ><i
                                                 class="fa-solid fa-pen-to-square" title="تعديل"></i></button>
 
                                         <button type="button" class="btn btn-danger btn-sm delete" data-url-delete="' . route('project.deposits.destroy') . '" 
                                         id="' . $row->id . '" title="حذف"><i
                                                 class="fa-solid fa-trash"></i></button>';
                        }

                        $output .= '<button type="button" class="btn btn-warning btn-sm showlogs ms-2" data-logs-type="2" data-url-logs="' . route('logs') . '" id="' . $row->id . '" title="سجلات"><i class="fa-solid fa-circle-question"></i></button>';

                        $output .= '</td>
                                 </tr>';
                    }
                    $output .= '</tbody></table></div>';
                    $output .= $data2->links();
                } else {
                    $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
                }
                return response()->json([
                    'table_data' => $output,
                    'sum' => number_format($sum, 0)
                ]);
            }
            return view('admin.project.showexpense');
        } else {
            abort(404);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if (\Auth::user()->rule != 'user4') {
            if ($request->ajax()) {
                $validated = $request->validate(
                    [
                        'day'                   => 'required',
                        'month'                 => 'required',
                        'year'                  => 'required',
                        'expense_door'          => 'required',
                        'expense_name'          => 'required',
                        'currency'              => 'required',
                        'expense_amount'        => 'required',
                        'expense_details'       => 'required',
                    ],
                    [
                        'day.required'                              => 'يرجى اختيار اليوم',
                        'month.required'                            => 'يرجى اختيار الشهر',
                        'year.required'                             => 'يرجى اختيار السنة',
                        'expense_door.required'                     => 'يرجى اختيار باب الصرف',
                        'expense_name.required'                     => 'يرجى ادخال اسم الصرف',
                        'currency.required'                         => 'يرجى اختيار العملة',
                        'expense_amount.required'                   => 'يرجى ادخال مبلغ الصرف',
                        'expense_details.required'                  => 'يرجى كتابة التفاصيل',
                    ]
                );

                if ($request->hasFile('expense_file')) {
                    $allfiles = array();
                    $files = $request->file('expense_file');

                    foreach ($files as $key => $file) {
                        if ($file->isValid()) {
                            $filePath = $file->store('uploads/expense', 'public');
                            $fileName = $filePath;
                            $allfiles[] = $fileName;
                        }
                    }
                    $empty_array = array($allfiles);
                    if (!empty($empty_array)) {
                        $res_file = implode(',', $allfiles);
                    } else {
                        $res_file = $request->expense_file_old;
                    }
                } else {
                    $res_file = $request->expense_file_old;
                }

                DB::table('project_deposits_expenses')
                    ->where('id', $request->id)
                    ->update(
                        [
                            'day' => $request->day,
                            'month' => $request->month,
                            'year' => $request->year,
                            'door' => $request->expense_door,
                            'date' => $request->year . '-' . $request->month . '-' . $request->day,
                            'name' => $request->expense_name,
                            'currency' => $request->currency,
                            'currency_price' => $request->currency_price,
                            'price_dollar' => $request->price_dollar,
                            'amount' => $request->expense_amount,
                            'details' => $request->expense_details,
                            'file' => $res_file,
                        ]
                    );

                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'تحديث مشروع', 'تم تحديث سند الصرف رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return response()->json(['state' => 'success']);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        if (\Auth::user()->rule != 'user4') {
            if ($request->ajax()) {
                DB::table('project_deposits_expenses')
                    ->where('id', $request->id)
                    ->update(['isDelete' => 1]);
                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'حذف مشروع', 'تم حذف سند الايداع رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return 'success';
            }
        }
    }
    public function printreportdaily(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', 2)->whereDate('created_at', '=', Carbon::today()->toDateString())->orderBy('id', 'desc')->latest()->get();
            $total_row = $deposit->count();
            $sum = 0;
            $sum2 = 0;
            if ($total_row > 0) {
                $output .= '<p>التاريخ: ' . Carbon::today()->toDateString() . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col" style="width: 56px;">رقم السند</th>
                                <th scope="col" style="width: 66px;">التاريخ</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">العملة</th>
                                <th scope="col">سعر الصرف</th>
                                <th scope="col">المبلغ بالدولار</th>
                                <th scope="col">اسم الصرف</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->date . '</td>
                                <td>' . DB::table('doors')->select('name')->where('id', $row->door)->first()->name . '</td>
                                <td>' . $row->amount . '</td>
                                <td>' . $row->currency . '</td>
                                <td>' . $row->currency_price . '</td>
                                <td>' . $row->price_dollar . '</td>
                                <td>' . $row->name . '</td>
                                <td>' . $row->details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->amount != null ? $row->amount : 0);
                    $sum2 += str_replace(',', '', $row->price_dollar != null ? $row->price_dollar : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="3">' . number_format($sum, 0) . ' دينار عراقي</td><td></td><td>المجموع بالدولار</td><td>' . number_format($sum2, 0) . ' دولار امريكي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة', 'تم طباعة سند الصرف المشروع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
    public function printreport(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', 2)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '') {
                        return $q->where('details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') == '') {
                        return $q->where('date', '=',  $request->get('query4'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') != '') {
                        return $q->where('date', '>=', $request->get('query4'))
                            ->where('date', '<=', $request->get('query5'));
                    }
                })
                ->orderBy('id', 'desc')
                ->get();
            $total_row = $deposit->count();
            $sum = 0;
            $sum2 = 0;
            if ($total_row > 0) {
                $output .= '<p>تم سحب هذا التقرير بتاريخ: ' . Carbon::today()->toDateString() . ' الساعة ' . date('h:i a') . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col" style="width: 56px;">رقم السند</th>
                                <th scope="col" style="width: 66px;">التاريخ</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">العملة</th>
                                <th scope="col">سعر الصرف</th>
                                <th scope="col">المبلغ بالدولار</th>
                                <th scope="col">اسم الصرف</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->date . '</td>
                                <td>' . DB::table('doors')->select('name')->where('id', $row->door)->first()->name . '</td>
                                <td>' . $row->amount . '</td>
                                <td>' . $row->currency . '</td>
                                <td>' . $row->currency_price . '</td>
                                <td>' . $row->price_dollar . '</td>
                                <td>' . $row->name . '</td>
                                <td>' . $row->details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->amount != null ? $row->amount : 0);
                    $sum2 += str_replace(',', '', $row->price_dollar != null ? $row->price_dollar : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="4">' . number_format($sum, 0) . ' دينار عراقي</td><td></td><td>المجموع بالدولار</td><td>' . number_format($sum2, 0) . ' دولار امريكي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة حسب الفرز', 'تم طباعة سند الصرف المشروع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
}
