<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserCode;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Auth;

class TwoFactorAuthController extends Controller
{
    use AuthenticatesUsers;
    protected $redirectTo = RouteServiceProvider::AUTHENTICATION;

    public function index()
    {
        if (Auth::user()->id == 1 || \Session::has('2fa')) {
            return redirect()->route('index');
        }

        $exists = UserCode::where('user_id', auth()->user()->id)
            ->where('updated_at', '>=', now()->subMinutes(5))
            ->exists();
        if (!$exists) {
            auth()->user()->generateCode();

            return view('2fa')
                ->with('success', 'لقد قمنا بإعادة إرسال OTP إلى رقم هاتفك المحمول.');
        } else {
            return view('2fa')
                ->with('error', 'يجب الانتظار 5 دقائق ليتم ارسال رمز تحقق جديد.');
        }
    }

    /**
     * validate sms
     *
     * @return response()
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required',
        ]);

        $exists = UserCode::where('user_id', auth()->user()->id)
            ->where('code', $validated['code'])
            ->where('updated_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($exists) {
            \Session::put('2fa', auth()->user()->id);

            return redirect()->route('index');
        }

        return redirect()
            ->back()
            ->with('error', 'لقد أدخلت رمز OTP خاطئًا.');
    }
    /**
     * resend otp code
     *
     * @return response()
     */
    public function resend()
    {
        $exists = UserCode::where('user_id', auth()->user()->id)
            ->where('updated_at', '>=', now()->subMinutes(5))
            ->exists();
        if (!$exists) {
            auth()->user()->generateCode();

            return back()
                ->with('success', 'لقد قمنا بإعادة إرسال OTP إلى رقم هاتفك المحمول.');
        } else {
            return back()
                ->with('error', 'يجب الانتظار 5 دقائق ليتم ارسال رمز تحقق جديد.');
        }
    }
}
