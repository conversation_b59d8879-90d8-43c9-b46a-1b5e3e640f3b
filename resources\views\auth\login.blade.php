<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>تسجيل الدخول الى نظام مجمع النجوم السكني</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('public/assets/images/logo2.webp')}}">

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/core/libs.min.css')}}">


    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/hope-ui.min.css?v=4.0.0')}}">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/custom.min.css?v=4.0.0')}}">

    <!-- Dark Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/dark.min.css')}}">

    <!-- Customizer Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/customizer.min.css')}}">

    <!-- RTL Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/rtl.min.css')}}">

    <!-- Style Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/style.css')}}">
</head>

<body class=" " data-bs-spy="scroll" data-bs-target="#elements-section" data-bs-offset="0" tabindex="0">
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body">
            </div>
        </div>
    </div>
    <!-- loader END -->

    <div class="wrapper">
        <section class="login-content">
            <div class="row m-0 align-items-center bg-white vh-100">
                <div class="col-md-6">
                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="card card-transparent shadow-none d-flex justify-content-center mb-0 auth-card">
                                <div class="card-body">
                                    <a href="#" class="navbar-brand d-flex align-items-center mb-3">

                                        <!--Logo start-->
                                        {{-- <div class="logo-main">
                                            <div class="logo-normal">
                                                <img src="{{ asset('public/assets/images/logo2.webp') }}" alt="logo"
                                                    width="30">
                                            </div>
                                            <div class="logo-mini">
                                                <img src="{{ asset('public/assets/images/logo2.webp') }}" alt="logo"
                                                    width="30">
                                            </div>
                                        </div> --}}
                                        <!--logo End-->
                                        {{-- <h4 class="logo-title ms-3">مجمع النجوم السكني</h4> --}}
                                    </a>
                                    <h2 class="mb-2 text-center">تسجيل الدخول</h2>
                                    <form action="{{ route('login') }}" method="POST">
                                        @csrf
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group">
                                                    <label for="username" class="form-label">اسم المستخدم</label>
                                                    <input type="text"
                                                        class="form-control @error('username') is-invalid @enderror"
                                                        id="username" name="username" aria-describedby="username"
                                                        value="{{ old('username') }}" required autocomplete="username"
                                                        autofocus>
                                                    @error('username')
                                                    <span class="invalid-feedback mt-3" role="alert">
                                                        <strong>اسم المستخدم او الباسوورد خطأ</strong>
                                                    </span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="form-group" style="position: relative">
                                                    <label for="password" class="form-label">كلمة السر</label>
                                                    <input type="password"
                                                        class="form-control @error('password') is-invalid @enderror"
                                                        name="password" required autocomplete="current-password"
                                                        id="password" aria-describedby="password">
                                                    <span id="showPassword"
                                                        style="position: absolute;left: 9px;top: 40px;cursor: pointer;"
                                                        onclick="myFunction()">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            fill="currentColor" class="bi bi-eye-fill"
                                                            viewBox="0 0 16 16">
                                                            <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0" />
                                                            <path
                                                                d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8m8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7" />
                                                        </svg>
                                                    </span>
                                                    @error('password')
                                                    <span class="invalid-feedback" role="alert">
                                                        <strong>{{ $message }}</strong>
                                                    </span>
                                                    @enderror
                                                </div>

                                            </div>
                                            <div class="col-lg-12 d-flex justify-content-between">
                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" name="remember"
                                                        id="remember" {{ old('remember') ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="remember">تذكرني</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-center">
                                            <button type="submit" class="btn btn-primary">دخول</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sign-bg">
                        <img src="{{ asset('public/assets/images/logo2.webp') }}" alt="logo" width="380" style="opacity: .1; margin-right: -100px;
    margin-top: -100px;">
                    </div>
                </div>
                <div class="col-md-6 d-md-block d-none p-0 mt-n1 vh-100 overflow-hidden" style="background: linear-gradient(90deg, #1c7b7c 0%, #0f4061 100%),
    linear-gradient(90deg, #1c7b7c 0%, #0f4061 100%);
      background-size: 400% 400%;
    animation: gradient 10s ease infinite;">
                    <img src="{{asset('public/assets/images/logo.webp')}}" class="img-fluid animated-scaleX"
                        alt="images" style="width:600px;margin-top: 51px;margin-right: 101px;">
                </div>
            </div>
        </section>
    </div>

    <!-- Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/libs.min.js')}}"></script>

    <!-- External Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/external.min.js')}}"></script>

    <!-- Widgetchart Script -->
    <script src="{{ asset('public/assets/js/charts/widgetcharts.js')}}"></script>

    <!-- mapchart Script -->
    <script src="{{ asset('public/assets/js/charts/vectore-chart.js')}}"></script>
    <script src="{{ asset('public/assets/js/charts/dashboard.js')}}"></script>

    <!-- fslightbox Script -->
    <script src="{{ asset('public/assets/js/plugins/fslightbox.js')}}"></script>

    <!-- Settings Script -->
    <script src="{{ asset('public/assets/js/plugins/setting.js')}}"></script>

    <!-- Slider-tab Script -->
    <script src="{{ asset('public/assets/js/plugins/slider-tabs.js')}}"></script>

    <!-- Form Wizard Script -->
    <script src="{{ asset('public/assets/js/plugins/form-wizard.js')}}"></script>

    <!-- App Script -->
    <script src="{{ asset('public/assets/js/hope-ui.js')}}" defer></script>

    <script>
        function myFunction() {
         var x = document.getElementById("password");
         if (x.type === "password") {
            x.type = "text";
         } else {
            x.type = "password";
         }
      }
    </script>
</body>

</html>