<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use App\Models\Client;
use App\Models\Payment;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Deposit;

class ClientController extends Controller
{
    /**
     * Store a new flight in the database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        if ($request->ajax()) {
            $output = '';
            $pagination = 10;

            $data = Client::where('isDelete', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('name_purshes', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%')
                            ->orWhere('house_no', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    $query2 = $request->get('query2');

                    if ($query2 != '') {
                        if ($query2 == 'غير محدد') {
                            return $q->where('sold_type', NULL);
                        } else {
                            return $q->where('sold_type', 'LIKE', '%' . str_replace(' ', '%', $query2) . '%');
                        }
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') == '') {
                        return $q->where('created_at', '=',  $request->get('query3'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') != '') {
                        return $q->where('created_at', '>=', $request->get('query3'))
                            ->where('created_at', '<=', $request->get('query4'));
                    }
                })
                ->orderBy('id', 'desc')
                ->paginate($pagination);
            $total_row = $data->count();
            if ($total_row > 0) {
                $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">التاريخ</th>
                                    <th scope="col">اسم العميل</th>
                                    <th scope="col">نوع البيع</th>
                                    <th scope="col">المحفظة</th>
                                    <th scope="col">الادارة</th>
                                </tr>
                            </thead><tbody>';
                foreach ($data as $key => $row) {

                    $output .= '<tr>
                                    <td>' . $row->id . '</td>
                                    <td>' . date('Y-m-d', strtotime($row->created_at)) . '</td>
                                    <td>' . $row->name_purshes . ' | ' . $row->house_no . '</td>
                                    <td>' . $row->sold_type . '</td>
                                    <td>' . DB::table('wallets')->where('client_id', $row->id)->first()->wallet_price . '</td>
                                    <td>
                                        <a href="' . route('clients.view', $row->id) . '" target="_blank" class="btn btn-info btn-sm viewclient me-2" data-url-view="" title="معاينة"><i
                                                class="fa-solid fa-eye"></i></a>';
                    if (\Auth::user()->rule == 'admin') {
                        $output .= '<a href="' . route('clients.show', $row->id) . '" class="btn btn-primary btn-sm" ><i class="fa-solid fa-pen-to-square" title="تعديل"></i></a>';

                        $output .= '<button type="button" class="btn btn-danger btn-sm deleteclient ms-2" data-url-delete="' . route('clients.destroy') . '" id="' . $row->id . '" title="حذف"><i
                                                            class="fa-solid fa-trash"></i></button>';
                    }
                    if (\Auth::user()->rule == 'admin' || \Auth::user()->rule == 'user4') {
                        $output .= '<button type="button" class="btn btn-warning btn-sm showlogs ms-2" data-logs-type="1" data-url-logs="' . route('logs') . '" id="' . $row->id . '" title="سجلات"><i class="fa-solid fa-circle-question"></i></button>';
                    }

                    $output .= '</td>
                                </tr>';
                }
                $output .= '</tbody></table></div>';
                $output .= $data->links();
            } else {
                $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
            }
            return response()->json([
                'table_data' => $output
            ]);
        }
        return view('admin.clients');
    }
    public function contracts(Request $request)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        if ($request->ajax()) {
            $output = '';
            $pagination = 10;

            $data = Client::where('isDelete', 0)->whereNotNull('sold_type')
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('name_purshes', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '' && $request->get('query3') == '') {
                        return $q->where('created_at', '=',  $request->get('query2'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '' && $request->get('query3') != '') {
                        return $q->where('created_at', '>=', $request->get('query2'))
                            ->where('created_at', '<=', $request->get('query3'));
                    }
                })
                ->orderBy('id', 'desc')
                ->paginate($pagination);
            $total_row = $data->count();
            if ($total_row > 0) {
                $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">التاريخ</th>
                                    <th scope="col">اسم العميل</th>
                                    <th scope="col">نوع البيع</th>
                                    <th scope="col">الادارة</th>
                                </tr>
                            </thead><tbody>';
                foreach ($data as $row) {

                    $output .= '<tr>
                                    <td>' . $row->id . '</td>
                                    <td>' . date('Y-m-d', strtotime($row->created_at)) . '</td>
                                    <td>' . $row->name_purshes . ' | ' . $row->house_no . '</td>
                                    <td>' . $row->sold_type . '</td>
                                    <td>
                                        <a href="' . route('contractsPrint', $row->id) . '" target="_blank" class="btn btn-info btn-sm" id="' . $row->id . '" title="عقد"><i class="fa-solid fa-file-contract"></i></a>
                                    </td>
                                </tr>';
                }
                $output .= '</tbody></table></div>';
                $output .= $data->links();
            } else {
                $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
            }
            return response()->json([
                'table_data' => $output
            ]);
        }
        return view('admin.contracts');
    }
    public function newclients()
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5') {
            abort(404);
        }
        return view('admin.newclients');
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5') {
            abort(404);
        }
        $id = \Auth::user()->id;
        $validated = $request->validate(
            [
                'house_no'                  => 'required',
                'house_space'               => 'required',
                'name_purshes'              => 'required',
            ]
        );
        $client =  Client::where('house_no', $request->house_no)->where('isDelete', 0)->first();
        if ($client) {
            toastr()->success('رقم الدار مسجل مسبقاً');
            return back();
        }
        if ($request->hasFile('files')) {
            $allfiles = array();
            $files = $request->file('files');

            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    $filePath = $file->store('uploads/clients', 'public');
                    $fileName = $filePath;
                    $allfiles[] = $fileName;
                }
            }
            $empty_array = array($allfiles);
            if (!empty($empty_array)) {
                $res_file = implode(',', $allfiles);
            } else {
                $res_file = NULL;
            }
        } else {
            $res_file = NULL;
        }
        $client_id = Client::create([
            'house_no'                      => $request->house_no,
            'house_space'                   => $request->house_space,
            'buld_space'                    => $request->buld_space,
            'rooms_no'                      => $request->rooms_no,
            'totol_house_price'             => $request->totol_house_price,
            'name_purshes'                  => $request->name_purshes,
            'name_mother_purshes'           => $request->name_mother_purshes,
            'phone1'                        => $request->phone1,
            'phone2'                        => $request->phone2,
            'id_no'                         => $request->id_no,
            'id_relese'                     => $request->id_relese,
            'id_relese_date'                => $request->id_relese_date,
            'address'                       => $request->address,
            'sold_type'                     => $request->sold_type,
            'sold_price'                    => $request->sold_price,
            'begen_price'                   => $request->begen_price,
            'begen_date'                    => $request->begen_date,
            'delvery_key_prise'             => $request->delvery_key_prise,
            'delvery_key_date'              => $request->delvery_key_date,
            'months_no'                     => $request->months_no,
            'years_no'                     => $request->years_no,
            'monthly_price'                 => $request->monthly_price,
            'annual_price'                  => $request->annual_price,
            'addprice'                     => $request->addprice,
            'prise_add'                     => $request->prise_add1,
            'prise_add2'                     => $request->prise_add2,
            'prise_add3'                     => $request->prise_add3,
            'prise_add4'                     => $request->prise_add4,
            'prise_add5'                     => $request->prise_add5,
            'prise_add6'                     => $request->prise_add6,
            'prise_add7'                     => $request->prise_add7,
            'prise_add8'                     => $request->prise_add8,
            'prise_add9'                     => $request->prise_add9,
            'prise_add10'                     => $request->prise_add10,
            'prise_add11'                     => $request->prise_add11,
            'prise_add12'                     => $request->prise_add12,
            'prise_add13'                     => $request->prise_add13,
            'prise_add14'                     => $request->prise_add14,
            'prise_add15'                     => $request->prise_add15,
            'prise_add16'                     => $request->prise_add16,
            'prise_add17'                     => $request->prise_add17,
            'prise_add18'                     => $request->prise_add18,
            'prise_add19'                     => $request->prise_add19,
            'prise_add20'                     => $request->prise_add20,
            'first_installment_date'        => $request->first_installment_date,
            'payments_no'                   => $request->payments_no,
            'files'                         => $res_file,
            'user_id'                       => $id,
        ]);
        DB::insert(
            'insert into wallets (client_id, created_at, updated_at) values (?, ?, ?)',
            [$client_id->id, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
        );
        if (!empty($request->sold_type)) {
            DB::insert(
                'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                [$client_id->id, $request->sold_type, 'مقدمة', $request->begen_price, $request->begen_date, date('Y-m-d H:i:s')]
            );

            if ($request->sold_type == 'اقساط') {
                $time = strtotime($request->first_installment_date);
                for ($i = 0; $i < $request->months_no; $i++) {

                    $final = date("Y-m-d H:i:s", strtotime("+" . $i . " month", $time));

                    DB::insert(
                        'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                        [$client_id->id, $request->sold_type, 'الاقساط الشهرية ' . ($i + 1), $request->monthly_price, $final, date('Y-m-d H:i:s')]
                    );
                }
                $annual_price = $request->years_no;
                for ($i = 1; $i <= $annual_price; $i++) {
                    $resannul = $request->annual_price;
                    if ($i == 1) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add . $i != null ? $request->prise_add1 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 2) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add2 != null ? $request->prise_add2 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 3) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add3 != null ? $request->prise_add3 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 4) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add4 != null ? $request->prise_add4 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 5) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add5 != null ? $request->prise_add5 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 6) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add6 != null ? $request->prise_add6 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 7) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add7 != null ? $request->prise_add7 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 8) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add8 != null ? $request->prise_add8 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 9) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add9 != null ? $request->prise_add9 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 10) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add10 != null ? $request->prise_add10 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 11) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add11 != null ? $request->prise_add11 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 12) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add12 != null ? $request->prise_add12 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 13) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add13 != null ? $request->prise_add13 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 14) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add14 != null ? $request->prise_add14 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 15) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add15 != null ? $request->prise_add15 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 16) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add16 != null ? $request->prise_add16 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 17) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add17 != null ? $request->prise_add17 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 18) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add18 != null ? $request->prise_add18 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 19) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add19 != null ? $request->prise_add19 : 0);
                        $resannul = number_format($sum, 0);
                    } elseif ($i == 20) {
                        $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add20 != null ? $request->prise_add20 : 0);
                        $resannul = number_format($sum, 0);
                    }
                    $final2 = date("Y-m-d H:i:s", strtotime("+" . $i . " year", $time));
                    DB::insert(
                        'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                        [$client_id->id, $request->sold_type, 'الاقساط السنوية ' . ($i), $resannul, $final2, date('Y-m-d H:i:s')]
                    );
                }
            }
            if ($request->sold_type == 'دفعات محددة') {
                $input = $request->all();
                $payment_price = $input['payment_price'];
                foreach ($payment_price as $key => $p) {
                    $payment = new Payment;
                    $payment->payment_price = $input['payment_price'][$key];
                    $payment->payment_date = $input['payment_date'][$key];
                    $payment->client_id = $client_id->id;
                    $payment->save();

                    DB::insert(
                        'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                        [$client_id->id, $request->sold_type, 'الدفعات المحددة' . ($key + 1), $input['payment_price'][$key], $input['payment_date'][$key], date('Y-m-d H:i:s')]
                    );
                }
            }
            DB::insert(
                'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                [$client_id->id, $request->sold_type, 'تسليم المفتاح', $request->delvery_key_prise, $request->delvery_key_date, date('Y-m-d H:i:s')]
            );
        }

        DB::insert(
            'insert into logs (user_id, client_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)',
            [\Auth::user()->id, $client_id->id, 'تحديث', 'تم اضافة العميل رقم الاي دي للعميل ' . $client_id->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
        );
        toastr()->success('تم اضافة العميل بنجاح');
        return back();
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5') {
            abort(404);
        }
        $id = \Auth::user()->id;
        $validated = $request->validate(
            [
                'house_no'                  => 'required',
                'house_space'               => 'required',
                'name_purshes'              => 'required',
            ]
        );

        if ($request->hasFile('files')) {
            $allfiles = array();
            $files = $request->file('files');

            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    $filePath = $file->store('uploads/clients', 'public');
                    $fileName = $filePath;
                    $allfiles[] = $fileName;
                }
            }
            $empty_array = array($allfiles);
            if (!empty($empty_array)) {
                $res_file = implode(',', $allfiles);
            } else {
                $res_file = $request->files_old;
            }
        } else {
            $res_file = $request->files_old;
        }
        $installments = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 1)
            ->first();
        $client = Client::find($request->id);

        $client->house_no                      = $request->house_no;
        $client->house_space                   = $request->house_space;
        $client->buld_space                    = $request->buld_space;
        $client->rooms_no                      = $request->rooms_no;
        $client->totol_house_price             = $request->totol_house_price;
        $client->name_purshes                  = $request->name_purshes;
        $client->name_mother_purshes           = $request->name_mother_purshes;
        $client->phone1                        = $request->phone1;
        $client->phone2                        = $request->phone2;
        $client->id_no                         = $request->id_no;
        $client->id_relese                     = $request->id_relese;
        $client->id_relese_date                = $request->id_relese_date;
        $client->address                       = $request->address;
        if (!$installments) {
            $client->sold_type                     = $request->sold_type;
            $client->sold_price                    = $request->sold_price;
            $client->begen_price                   = $request->begen_price;
            $client->begen_date                    = $request->begen_date;
            $client->delvery_key_prise             = $request->delvery_key_prise;
            $client->delvery_key_date              = $request->delvery_key_date;
            $client->months_no                     = $request->months_no;
            $client->years_no                     = $request->years_no;
            $client->monthly_price                 = $request->monthly_price;
            $client->annual_price                  = $request->annual_price;
            $client->addprice                      = $request->addprice;
            $client->prise_add                      = $request->prise_add1;
            $client->prise_add2                      = $request->prise_add2;
            $client->prise_add3                      = $request->prise_add3;
            $client->prise_add4                      = $request->prise_add4;
            $client->prise_add5                      = $request->prise_add5;
            $client->prise_add6                      = $request->prise_add6;
            $client->prise_add7                      = $request->prise_add7;
            $client->prise_add8                      = $request->prise_add8;
            $client->prise_add9                      = $request->prise_add9;
            $client->prise_add10                      = $request->prise_add10;
            $client->prise_add11                      = $request->prise_add11;
            $client->prise_add12                      = $request->prise_add12;
            $client->prise_add13                      = $request->prise_add13;
            $client->prise_add14                      = $request->prise_add14;
            $client->prise_add15                      = $request->prise_add15;
            $client->prise_add16                      = $request->prise_add16;
            $client->prise_add17                      = $request->prise_add17;
            $client->prise_add18                      = $request->prise_add18;
            $client->prise_add19                      = $request->prise_add19;
            $client->prise_add20                      = $request->prise_add20;
            $client->first_installment_date        = $request->first_installment_date;
            $client->payments_no                   = $request->payments_no;
        }
        $client->files                         = $res_file;
        $client->user_id                       = $id;

        $client->save();
        Deposit::where('deposit_name', $request->id)
            ->update(['house_no' => $request->house_no]);

        if (!$installments) {
            if (!empty($request->sold_type)) {
                DB::table('installments')
                    ->where('client_id', '=', $request->id)
                    ->delete();
                Payment::where('client_id', $request->id)->delete();
                DB::insert(
                    'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                    [$request->id, $request->sold_type, 'مقدمة', $request->begen_price, $request->begen_date, date('Y-m-d H:i:s')]
                );
                if ($request->sold_type == 'دفعات محددة') {
                    $input = $request->all();
                    $payment_price = $input['payment_price'];
                    foreach ($payment_price as $key => $p) {
                        $payment = new Payment;
                        $payment->payment_price = $input['payment_price'][$key];
                        $payment->payment_date = $input['payment_date'][$key];
                        $payment->client_id = $request->id;
                        $payment->save();

                        DB::insert(
                            'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                            [$request->id, $request->sold_type, 'الدفعات المحددة' . ($key + 1), $input['payment_price'][$key], $input['payment_date'][$key], date('Y-m-d H:i:s')]
                        );
                    }
                } elseif ($request->sold_type == 'اقساط') {
                    $time = strtotime($request->first_installment_date);
                    for ($i = 0; $i < $request->months_no; $i++) {

                        $final = date("Y-m-d H:i:s", strtotime("+" . $i . " month", $time));

                        DB::insert(
                            'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                            [$request->id, $request->sold_type, 'الاقساط الشهرية ' . ($i + 1), $request->monthly_price, $final, date('Y-m-d H:i:s')]
                        );
                    }
                    $annual_price = $request->years_no;
                    for ($i = 1; $i <= $annual_price; $i++) {
                        $resannul = $request->annual_price;
                        if ($i == 1) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add1 != null ? $request->prise_add1 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 2) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add2 != null ? $request->prise_add2 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 3) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add3 != null ? $request->prise_add3 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 4) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add4 != null ? $request->prise_add4 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 5) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add5 != null ? $request->prise_add5 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 6) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add6 != null ? $request->prise_add6 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 7) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add7 != null ? $request->prise_add7 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 8) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add8 != null ? $request->prise_add8 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 9) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add9 != null ? $request->prise_add9 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 10) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add10 != null ? $request->prise_add10 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 11) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add11 != null ? $request->prise_add11 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 12) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add12 != null ? $request->prise_add12 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 13) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add13 != null ? $request->prise_add13 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 14) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add14 != null ? $request->prise_add14 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 15) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add15 != null ? $request->prise_add15 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 16) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add16 != null ? $request->prise_add16 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 17) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add17 != null ? $request->prise_add17 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 18) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add18 != null ? $request->prise_add18 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 19) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add19 != null ? $request->prise_add19 : 0);
                            $resannul = number_format($sum, 0);
                        } elseif ($i == 20) {
                            $sum = str_replace(',', '', $request->annual_price != null ? $request->annual_price : 0) + str_replace(',', '', $request->prise_add20 != null ? $request->prise_add20 : 0);
                            $resannul = number_format($sum, 0);
                        }
                        $final2 = date("Y-m-d H:i:s", strtotime("+" . $i . " year", $time));
                        DB::insert(
                            'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                            [$request->id, $request->sold_type, 'الاقساط السنوية ' . ($i), $resannul, $final2, date('Y-m-d H:i:s')]
                        );
                    }
                }

                DB::insert(
                    'insert into installments (client_id, sold_type, price_name, price, price_date, created_at) values (?, ?, ?, ?, ?, ?)',
                    [$request->id, $request->sold_type, 'تسليم المفتاح', $request->delvery_key_prise, $request->delvery_key_date, date('Y-m-d H:i:s')]
                );
            } else {
                DB::table('installments')
                    ->where('client_id', '=', $request->id)
                    ->delete();
                Payment::where('client_id', $request->id)->delete();
            }
        }

        DB::insert('insert into logs (user_id, client_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'تحديث', 'تم تحديث العميل رقم الاي دي للعميل ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
        toastr()->success('تم تحديث العميل بنجاح');
        // return redirect()->back();
        return redirect()->route('clients');
    }
    /**
     * Store a new flight in the database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function show($id)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        $client =  Client::where('id', $id)->first();
        $payments =  Payment::where('client_id', $client->id)->get();
        $installments = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->where('status', '=', 1)
            ->first();
        return view('admin.editclients', ['client' => $client, 'payments' => $payments, 'installments' => $installments]);
    }
    public function view($id)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        $client =  Client::where('id', $id)->first();
        if (!$client) {
            abort(404);
        }
        $payments =  Payment::where('client_id', $client->id)->get();
        $installments = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->take(36)->orderBy('price_date', 'asc')->get();
        $installments2 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(36)->take(36)->orderBy('price_date', 'asc')->get();
        $installments3 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(72)->take(39)->orderBy('price_date', 'asc')->get();
        $installments4 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(111)->take(39)->orderBy('price_date', 'asc')->get();
        $installments5 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(150)->take(39)->orderBy('price_date', 'asc')->get();
        $installments6 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(189)->take(39)->orderBy('price_date', 'asc')->get();
        $installments7 = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->skip(228)->take(39)->orderBy('price_date', 'asc')->get();
        $installmentstotal = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->get();
        return view('admin.viewclient', [
            'client' => $client,
            'payments' => $payments,
            'installments' => $installments,
            'installments2' => $installments2,
            'installments3' => $installments3,
            'installments4' => $installments4,
            'installments5' => $installments5,
            'installments6' => $installments6,
            'installments7' => $installments7,
            'installmentstotal' => $installmentstotal,
        ]);
    }
    public function contractsPrint($id)
    {
        if (\Auth::user()->rule != 'user2' && \Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user5' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        $client =  Client::where('id', $id)->first();
        $payments =  Payment::where('client_id', $client->id)->get();
        $installments = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->orderBy('price_date', 'desc')->first();
        $installments_count = DB::table('installments')
            ->where('client_id', '=', $client->id)
            ->count();
        return view('admin.contracts-print', [
            'client' => $client,
            'payments' => $payments,
            'installments' => $installments,
            'installments_count' => $installments_count,
        ]);
    }
    public function fetch(Request $request)
    {
        $installmentsarr = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 0)
            ->take(6)->orderBy('price_date', 'asc')->get();
        $installmentsarr2 = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 0)
            ->skip(6)->take(6)->orderBy('price_date', 'asc')->get();
        $installments = '<div class="col-sm-6"><div class="row">';
        foreach ($installmentsarr as $key => $value) {
            if ($value) {
                $installments .=
                    '<div class="col-sm-12"><div class="form-check">' .
                    '<input type="hidden" value="" name="installments_id[]" id="installments_id' .
                    ($key + 1) .
                    '">' .
                    '<input class="form-check-input" data-get-id="' .
                    $value->id .
                    '" data-id="installments_id' .
                    ($key + 1) .
                    '" type="checkbox" name="price_name" data-price="' .
                    $value->price .
                    '" value="' .
                    $value->price_name .
                    '" id="price_name' .
                    ($key + 1) .
                    '">' .
                    '<label class="form-check-label" for="price_name' .
                    ($key + 1) .
                    '">' .
                    $value->price_name .
                    " (" .
                    date("d", strtotime($value->price_date)) .
                    "/" .
                    date("m", strtotime($value->price_date)) .
                    "/" .
                    date("Y", strtotime($value->price_date)) .
                    ")" .
                    " (" .
                    $value->price .
                    ")" .
                    "</label>" .
                    "</div></div>";
            } else {
                $installments .=
                    "<p class='text-danger'>لا يوجد اقساط متبقية على هذا العميل</p>";
            }
        }
        $installments .= '</div></div>';
        $installments .= '<div class="col-sm-6"><div class="row">';
        foreach ($installmentsarr2 as $key => $value) {
            if ($value) {
                $installments .=
                    '<div class="col-sm-12"><div class="form-check">' .
                    '<input type="hidden" value="" name="installments_id[]" id="installments_id' .
                    ($key + 7) .
                    '">' .
                    '<input class="form-check-input" data-get-id="' .
                    $value->id .
                    '" data-id="installments_id' .
                    ($key + 7) .
                    '" type="checkbox" name="price_name" data-price="' .
                    $value->price .
                    '" value="' .
                    $value->price_name .
                    '" id="price_name' .
                    ($key + 7) .
                    '">' .
                    '<label class="form-check-label" for="price_name' .
                    ($key + 7) .
                    '">' .
                    $value->price_name .
                    " (" .
                    date("d", strtotime($value->price_date)) .
                    "/" .
                    date("m", strtotime($value->price_date)) .
                    "/" .
                    date("Y", strtotime($value->price_date)) .
                    ")" .
                    " (" .
                    $value->price .
                    ")" .
                    "</label>" .
                    "</div></div>";
            } else {
                $installments .=
                    "<p class='text-danger'>لا يوجد اقساط متبقية على هذا العميل</p>";
            }
        }
        $installments .= '</div></div>';
        $wallets = DB::table('wallets')
            ->where('client_id', '=', $request->id)
            ->first();
        return response()->json([
            'installments' => $installments,
            'wallets' => $wallets,
        ]);
    }
    public function installmentsfetch(Request $request)
    {
        $installments_paid = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 1)
            ->count();
        $installments_unpaid = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 0)
            ->count();
        $installments_total = DB::table('installments')
            ->where('client_id', '=', $request->id)
            ->where('status', '=', 0)
            ->get();

        $sum = 0;
        foreach ($installments_total as $item) {
            $sum += str_replace(',', '', $item->price != null ? $item->price : 0);
        }
        $total = number_format($sum, 0);
        return response()->json([
            'installments_paid' => $installments_paid,
            'installments_unpaid' => $installments_unpaid,
            'total' => $total,
        ]);
    }
    public function check(Request $request)
    {
        $client =  Client::where('house_no', $request->houseno)->where('isDelete', 0)->first();
        if ($client) {
            return 'exist';
        } else {
            return 'no exist';
        }
    }
    public function destroy(Request $request)
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        if ($request->ajax()) {
            $checkclient =  Client::where('id', $request->id)->where('isDelete', 0)->first();
            if ($checkclient['sold_type'] == null) {
                $client = Client::find($request->id);

                $client->isDelete = 1;

                $client->save();
                DB::insert('insert into logs (user_id, client_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'حذف', 'تم حذف العميل رقم الاي دي للعميل ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return 'success';
            } else {
                return 'error';
            }
        }
    }
    public function printreport(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $clients = Client::where('isDelete', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('name_purshes', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%')
                            ->orWhere('house_no', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    $query2 = $request->get('query2');

                    if ($query2 != '') {
                        if ($query2 == 'غير محدد') {
                            return $q->where('sold_type', NULL);
                        } else {
                            return $q->where('sold_type', 'LIKE', '%' . str_replace(' ', '%', $query2) . '%');
                        }
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') == '') {
                        return $q->where('created_at', '=',  $request->get('query3'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') != '') {
                        return $q->where('created_at', '>=', $request->get('query3'))
                            ->where('created_at', '<=', $request->get('query4'));
                    }
                })
                ->orderBy('id', 'desc')
                ->get();
            $total_row = $clients->count();
            if ($total_row > 0) {
                $output .= '<p>تم سحب هذا التقرير بتاريخ: ' . Carbon::today()->toDateString() . ' الساعة ' . date('h:i a') . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">التاريخ</th>
                                    <th scope="col">اسم العميل</th>
                                    <th scope="col">نوع البيع</th>
                                    <th scope="col">المحفظة</th>
                            </tr>
                        </thead><tbody>';
                foreach ($clients as $key => $row) {
                    $output .= '<tr>
                                    <td>' . ($key + 1) . '</td>
                                    <td>' . date('Y-m-d', strtotime($row->created_at)) . '</td>
                                    <td>' . $row->name_purshes . ' | ' . $row->house_no . '</td>
                                    <td>' . $row->sold_type . '</td>
                                    <td>' . DB::table('wallets')->where('client_id', $row->id)->first()->wallet_price . '</td>
                            </tr>';
                }
                $output .= '</tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة حسب الفرز', 'تم طباعة العملاء بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
}
