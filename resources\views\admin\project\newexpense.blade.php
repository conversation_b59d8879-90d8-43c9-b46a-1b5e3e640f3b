@extends('admin.project.layouts.app2')

@section('title', 'اضافة صرف جديد')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">
                    <h4 style="font-weight: 700;font-size: 17px;">اضافة صرف جديد</h4>
                    @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
                <div class="card-body">
                    <form class="newExpense" data-form-expense-url="{{route('project.expense.store')}}" method="POST"
                        enctype="multipart/form-data">
                        @csrf
                        <label class="form-label" for="input1">تاريخ الصرف</label>
                        <div style="display: flex;align-items: center;">
                            <div class="me-5 ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input1">اليوم</label>
                                    <select class="form-select" id="input1" style="width: 95px;" name="day" required>
                                        @for ($i = 1; $i <= 31; $i++) <option value="{{$i}}" {{date("d")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="me-5 ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input2">الشهر</label>
                                    <select class="form-select" id="input2" style="width: 95px;" name="month" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 1; $i <= 12; $i++) <option value="{{$i}}" {{date("m")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input3">السنة</label>
                                    <select class="form-select" id="input3" style="width: 150px;" name="year" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 2023; $i <= date("Y"); $i++) <option value="{{$i}}" {{date("Y")==$i
                                            ? 'selected' : '' }}>{{$i}}
                                            </option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <div class="col-4">
                                <label class="form-label" for="input4">باب الصرف</label>
                                <select class="form-select" id="input4" data-type-door="2" name="expense_door" required>
                                </select>
                            </div>
                            <div class="col-8">
                                <label for="input55" class="form-label">اسم الصرف</label>
                                <input class="form-control" id="input55" name="expense_name" required>
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <div class="col-3">
                                <label for="input5555" class="form-label">العملة</label>
                                <select name="currency" class="form-control" id="input5555" required>
                                    <option value="IQD">IQD</option>
                                    <option value="USD">USD</option>
                                </select>
                            </div>
                            <div class="col-3 hidecurrencyinput" style="display: none">
                                <label for="input777" class="form-label">سعر الصرف للدولار الواحد</label>
                                <input class="form-control text-dark" style="font-weight:bold" type="text" id="input777"
                                    data-type="currency">
                            </div>
                            <div class="col-3 hidecurrencyinput" style="display: none">
                                <label for="input7777" class="form-label">المبلغ بالدولار</label>
                                <input class="form-control text-dark" style="font-weight:bold" type="text"
                                    id="input7777" data-type="currency">
                            </div>
                            <div class="col-3">
                                <label for="input77" class="form-label">مبلغ الصرف</label>
                                <input class="form-control text-dark" style="font-weight:bold" type="text" id="input77"
                                    name="expense_amount" data-type="currency" required>
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <label for="input8" class="form-label">التفاصيل</label>
                            <input class="form-control" type="text" id="input8" name="expense_details" required>
                        </div>
                        <div class="d-flex justify-content-between align-items-baseline">
                            <div class="form-group mt-30 " style="width: 60px;">
                                <label for="input9" class="btn btn-custom filupp">
                                    <i class="fa-solid fa-upload"></i>
                                    <input type="file" multiple name="expense_file[]" class="d-none" value="1"
                                        id="input9" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xlsx" />
                                </label>
                            </div>
                            <div>
                                <span class="filupp-file-name js-value ms-5"></span>
                            </div>
                            <div class="form-group mt-3 text-end">
                                <button type="submit" class="btn btn-success btn-custom btn-send">اضافة</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-12 col-lg-4">
    <input type="hidden" value="{{route('amounts')}}" id="getAmountUrl">
    <div class="row">
        <div class="col-md-12 col-lg-12">
            <div class="card credit-card-widget" data-aos="fade-up" data-aos-delay="900"
                style="background-color: #e6ddd8">

                <div class="pb-4 border-0 card-header credit-card-widget-hidden-number">
                    <div class="p-4 border border-white rounded primary-gradient-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-center">
                                <h6 class="mb-1">الصندوق</h6>
                                <h6 class="mb-3 hiddenNumber totalAmount"></h6>
                                <h6 class="mb-1">مجموع الايداع الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalDeposits"></h6>
                                <h6 class="mb-1">مجموع الصرف الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalExpenses"></h6>
                            </div>
                            <div class="master-card-content">
                                <img src="{{ asset('public/assets/images/iqd.webp') }}" alt="logo" width="100">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body text-center">
                    <img src="{{ asset('public/assets/images/download.gif') }}" alt="logo" width="260">
                </div>
            </div>
        </div>
    </div>
</div>
@endsection