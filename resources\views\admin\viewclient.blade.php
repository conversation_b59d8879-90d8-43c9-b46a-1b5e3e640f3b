<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"
        integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous">

    <title>معاينة</title>

    <style>
        th,
        td {
            font-size: 12px;
        }

        .table>:not(caption)>*>* {
            padding: 2px;
        }

        p {
            font-size: 13px;
        }
    </style>
</head>

<body>

    <div class="container-fluid">
        <h6 class="text-center">ملحق تفاصيل الاقساط والدفعات دار رقم ({{$client->house_no}}) مجمع النجوم السكني</h6>
        <div style="display: flex;flex-direction: row;justify-content: space-between;">
            <p>نوع البيع: {{$client->sold_type}} <span class="ms-5">رقم الدار: {{$client->house_no}}</span></p>
            <p>اسم المشتري: {{$client->name_purshes}}</p>
            <p>رقم الهاتف: {{$client->phone1}}</p>
        </div>
        <?php
        $sum = 0;
        $sum2 = 0;
        foreach ($installmentstotal as $row) {
            if ($row->status == 0) { 
                $sum += str_replace(',', '', $row->price != null ? $row->price : 0);
            } else {
                $sum2 += str_replace(',', '', $row->price != null ? $row->price : 0);
            }
        }      
?>
        <table class="table table table-striped table-bordered text-center">
            <thead>
                <tr>
                    <th scope="col">المبلغ الكلي</th>
                    <th scope="col">المدفوع</th>
                    <th scope="col">المتبقي</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{$client->sold_price}}</td>
                    <td>{{number_format($sum2, 0)}}</td>
                    <td>{{number_format($sum, 0)}}</td>
                </tr>
            </tbody>
        </table>
        <div class="row">
            <div class="col-6">
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-6">
                @if (count($installments2) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments2 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
            <div class="col-6">
                @if (count($installments3) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments3 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
            <div class="col-6">
                @if (count($installments4) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments4 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
            <div class="col-6">
                @if (count($installments5) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments5 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
            <div class="col-6">
                @if (count($installments6) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments6 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
            <div class="col-6">
                @if (count($installments7) > 0)
                <table class="table table-bordered text-center table-striped">
                    <thead>
                        <tr>
                            <th scope="col">القسط</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">الاستحقاق</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($installments7 as $row)
                        <td>
                            <?php
                            if ($row->sold_type == 'اقساط') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الاقساط الشهرية","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            if ($row->sold_type == 'نقداً') {
                                echo $row->price_name;
                            }
                            if ($row->sold_type == 'دفعات محددة') {
                                if ($row->price_name == 'مقدمة') {
                                    echo $row->price_name;
                                }
                                if ($row->price_name != 'مقدمة' && $row->price_name != 'تسليم المفتاح') {
                                    echo str_replace("الدفعات المحددة","",$row->price_name);
                                }
                                if ($row->price_name == 'تسليم المفتاح') {
                                    echo $row->price_name;
                                }
                            }
                            ?>
                        </td>
                        <td>{{$row->price}}</td>
                        <td>{{date('Y-m-d', strtotime($row->price_date))}}</td>
                        <td>
                            <?php 
                                if ($row->status == 0 && date('Y-m-d', strtotime($row->price_date)) < date('Y-m-d')) 
                                { 
                                    echo 'متاخرة';
                                }
                                if ($row->status == 1)
                                {
                                    echo 'دفعت';
                                }
                            ?>
                        </td>
                        <td>{{$row->updated_at ? date('Y-m-d', strtotime($row->updated_at)) : '' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
            </div>
        </div>
    </div>
    <button style='position: fixed; left: 300px;top: 0' class='btn btn-primary'
        onclick="this.style.display='none';window.print();">طباعة</button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
</body>

</html>