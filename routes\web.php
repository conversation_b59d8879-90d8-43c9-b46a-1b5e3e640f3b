<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\DepositController;
use App\Http\Controllers\Admin\ExpenseController;
use App\Http\Controllers\Admin\ClientController;
use App\Http\Controllers\Admin\ProjectController;
use App\Http\Controllers\Admin\Project2Controller;
use App\Http\Controllers\TwoFactorAuthController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['prefix' => '/', 'namespace' => 'Admin', 'middleware' => ['auth', 'admin']], function () {
    // Index
    Route::get('/', [AdminController::class, 'index'])->name('index');

    // Users
    Route::get('/users', [AdminController::class, 'users'])->name('admin.users');
    Route::get('/profile', [AdminController::class, 'profile'])->name('admin.profile');
    Route::post('/profile/update', [AdminController::class, 'profileUpdate'])->name('admin.profile.update');
    Route::get('/users/new', [AdminController::class, 'newusers'])->name('admin.newusers');
    Route::post('/user/update', [AdminController::class, 'userUpdate'])->name('admin.user.update');
    Route::post('/users/store', [AdminController::class, 'usersStore'])->name('admin.users.store');
    Route::post('/users/delete', [AdminController::class, 'deleteuser'])->name('admin.users.delete');

    // Deposits
    Route::get('/deposits/show', [DepositController::class, 'show'])->name('deposits.show');
    Route::get('/deposits/new', [DepositController::class, 'create'])->name('deposits.create');
    Route::post('/deposits/store', [DepositController::class, 'store'])->name('deposits.store');
    Route::post('/deposits/destroy', [DepositController::class, 'destroy'])->name('deposits.destroy');
    Route::post('/deposits/printreportdaily', [DepositController::class, 'printreportdaily'])->name('deposits.printreportdaily');
    Route::get('/deposits/printreport', [DepositController::class, 'printreport'])->name('deposits.printreport');
    Route::post('/deposits/update', [DepositController::class, 'update'])->name('deposits.update');

    // Expense
    Route::get('/expense/show', [ExpenseController::class, 'show'])->name('expense.show');
    Route::get('/expense/new', [ExpenseController::class, 'create'])->name('expense.create');
    Route::post('/expense/store', [ExpenseController::class, 'store'])->name('expense.store');
    Route::post('/expense/destroy', [ExpenseController::class, 'destroy'])->name('expense.destroy');
    Route::post('/expense/printreportdaily', [ExpenseController::class, 'printreportdaily'])->name('expense.printreportdaily');
    Route::get('/expense/printreport', [ExpenseController::class, 'printreport'])->name('expense.printreport');
    Route::post('/expense/update', [ExpenseController::class, 'update'])->name('expense.update');

    // Doors
    Route::post('/doors/show', [AdminController::class, 'doors'])->name('doors.show');
    Route::post('/doors/store', [AdminController::class, 'storedoors'])->name('doors.storedoors');
    Route::post('/doors/destroy', [AdminController::class, 'destroydoors'])->name('doors.destroydoors');

    // Amounts
    Route::get('/amounts', [AdminController::class, 'amounts'])->name('amounts');

    // Clients
    Route::get('/clients', [ClientController::class, 'index'])->name('clients');
    Route::get('/clients/new', [ClientController::class, 'newclients'])->name('clients.new');
    Route::post('/clients/store', [ClientController::class, 'store'])->name('clients.store');
    Route::get('/clients/show/{id}', [ClientController::class, 'show'])->name('clients.show');
    Route::post('/clients/update', [ClientController::class, 'update'])->name('clients.update');
    Route::post('/clients/destroy', [ClientController::class, 'destroy'])->name('clients.destroy');
    Route::post('/clients/fetch', [ClientController::class, 'fetch'])->name('clients.fetch');
    Route::get('/clients/check', [ClientController::class, 'check'])->name('clients.check');
    Route::get('/clients/view/{id}', [ClientController::class, 'view'])->name('clients.view');
    Route::post('/clients/installments/fetch', [ClientController::class, 'installmentsfetch'])->name('clients.installments.fetch');
    Route::get('/contracts/{id}', [ClientController::class, 'contractsPrint'])->name('contractsPrint');
    Route::get('/clients/printreport', [ClientController::class, 'printreport'])->name('clients.printreport');
    // Contracts
    Route::get('/contracts', [ClientController::class, 'contracts'])->name('contracts');

    // logs
    Route::post('/logs', [AdminController::class, 'logs'])->name('logs');

    // Project deposits Index
    Route::get('/project', [ProjectController::class, 'index'])->name('project.deposits.index');
    Route::get('/project/deposits/show', [ProjectController::class, 'show'])->name('project.deposits.show');
    Route::get('/project/deposits/new', [ProjectController::class, 'create'])->name('project.deposits.create');
    Route::post('/project/deposits/store', [ProjectController::class, 'store'])->name('project.deposits.store');
    Route::post('/project/deposits/destroy', [ProjectController::class, 'destroy'])->name('project.deposits.destroy');
    Route::post('/project/deposits/printreportdaily', [ProjectController::class, 'printreportdaily'])->name('project.deposits.printreportdaily');
    Route::get('/project/deposits/printreport', [ProjectController::class, 'printreport'])->name('project.deposits.printreport');
    Route::post('/project/deposits/update', [ProjectController::class, 'update'])->name('project.deposits.update');

    // Project expense Index
    Route::get('/project/expense/show', [Project2Controller::class, 'show'])->name('project.expense.show');
    Route::get('/project/expense/new', [Project2Controller::class, 'create'])->name('project.expense.create');
    Route::post('/project/expense/store', [Project2Controller::class, 'store'])->name('project.expense.store');
    Route::post('/project/expense/destroy', [Project2Controller::class, 'destroy'])->name('project.expense.destroy');
    Route::post('/project/expense/printreportdaily', [Project2Controller::class, 'printreportdaily'])->name('project.expense.printreportdaily');
    Route::get('/project/expense/printreport', [Project2Controller::class, 'printreport'])->name('project.expense.printreport');
    Route::post('/project/expense/update', [Project2Controller::class, 'update'])->name('project.expense.update');
});
Auth::routes([
    'register' => false, // Disable Registration Routes...
    'reset' => false, // Disable Password Reset Routes...
    'verify' => false, // Disable Email Verification Routes...
]);
Route::get('two-factor-authentication', [TwoFactorAuthController::class, 'index'])->middleware('auth')->name('check2fa.index');
Route::post('two-factor-authentication', [TwoFactorAuthController::class, 'store'])->middleware('auth')->name('check2fa.store');
Route::get('two-factor-authentication/resend', [TwoFactorAuthController::class, 'resend'])->middleware('auth')->name('check2fa.resend');
