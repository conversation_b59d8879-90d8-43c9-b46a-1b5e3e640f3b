<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\Deposit;
use App\Models\Expense;

class AdminController extends Controller
{
    /**
     * Store a new flight in the database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function index()
    {
        if (\Auth::user()->rule == 'user6') {
            return view('admin.project.index');
        } else {
            return view('admin.index');
        }
    }
    // Users
    public function users()
    {
        if (\Auth::user()->rule != 'admin' && \Auth::user()->rule != 'user4') {
            abort(404);
        }
        $users = User::where('id', '!=', 1)->orderBy('id', 'desc')->paginate(10);
        return view('admin.users', ['users' => $users]);
    }
    // Profile
    public function profile()
    {
        return view('admin.profile');
    }
    public function profileUpdate(Request $request)
    {
        if ($request->id) {
            $id = $request->id;
        } else {
            $id = \Auth::user()->id;
        }
        $validated = $request->validate(
            [
                'name'              => 'required',
                'mobile_number'     => 'required|unique:users,mobile_number,' . $id,
                'username'          => 'required|unique:users,username,' . $id,
                'password' => 'confirmed',
            ],
            [
                'name.required'                     => 'يرجى كتابة الاسم',
                'mobile_number.required'            => 'يرجى كتابة رقم الهاتف',
                'mobile_number.unique'              => 'رقم الهاتف مسجل مسبقاً',
                'username.required'                 => 'يرجى كتابة اسم المستخدم',
                'username.unique'                   => 'اسم المستخدم مسجل مسبقاً',
                'password.confirmed'                => 'كلمة السر غير متطابقة!',
            ]
        );

        if (empty($request->password)) {
            $npass = $request->old_password;
        } else {
            $npass = Hash::make($request->password);
        }
        $user = User::find($id);

        $user->name             = $request->name;
        $user->mobile_number    = $request->mobile_number;
        $user->username         = $request->username;
        $user->password         = $npass;
        $user->save();

        toastr()->success('تم حفظ المعلومات بنجاح');
        return back();
    }
    public function userUpdate(Request $request)
    {
        if ($request->id) {
            $id = $request->id;
        } else {
            $id = \Auth::user()->id;
        }
        $validated = $request->validate(
            [
                'name'              => 'required',
                'mobile_number'     => 'required|unique:users,mobile_number,' . $id,
                'username'          => 'required|unique:users,username,' . $id,
                'rule_name'         => 'required',
                'password' => 'confirmed',
            ],
            [
                'name.required'                     => 'يرجى كتابة الاسم',
                'mobile_number.required'            => 'يرجى كتابة رقم الهاتف',
                'mobile_number.unique'              => 'رقم الهاتف مسجل مسبقاً',
                'username.required'                 => 'يرجى كتابة اسم المستخدم',
                'rule_name.required'                => 'يرجى اختيار صلاحية المستخدم',
                'username.unique'                   => 'اسم المستخدم مسجل مسبقاً',
                'password.confirmed'                => 'كلمة السر غير متطابقة!',
            ]
        );
        $is_deposits = '0';
        $is_deposits_new = '0';
        $is_expenses = '0';
        $is_expenses_new = '0';

        if ($request->rule_name == 'admin') {
            $rule = 'مسؤول النظام';
            $is_deposits = '1';
            $is_deposits_new = '1';
            $is_expenses = '1';
            $is_expenses_new = '1';
        } elseif ($request->rule_name == 'user1') {
            $rule = 'الحسابات';
        } elseif ($request->rule_name == 'user2') {
            $rule = 'المبيعات';
        } elseif ($request->rule_name == 'user3') {
            $rule = 'القانونية';
        } elseif ($request->rule_name == 'user4') {
            $rule = 'المراقب';
        } elseif ($request->rule_name == 'user5') {
            $rule = 'حسابات ومبيعات';
            $is_deposits = '1';
            $is_deposits_new = '1';
            $is_expenses = '1';
            $is_expenses_new = '1';
        }
        if ($request->rule_name == 'user1') {
            if ($request->is_deposits == '1') {
                $is_deposits = '1';
            }
            if ($request->is_deposits_new == '1') {
                $is_deposits_new = '1';
            }
            if ($request->is_expenses == '1') {
                $is_expenses = '1';
            }
            if ($request->is_expenses_new == '1') {
                $is_expenses_new = '1';
            }
        }
        if (empty($request->password)) {
            $npass = $request->old_password;
        } else {
            $npass = Hash::make($request->password);
        }
        $user = User::find($id);

        $user->name                 = $request->name;
        $user->mobile_number        = $request->mobile_number;
        $user->username             = $request->username;
        $user->rule                 = $request->rule_name;
        $user->rule_name            = $rule;
        $user->is_deposits          = $is_deposits;
        $user->is_deposits_new      = $is_deposits_new;
        $user->is_expenses          = $is_expenses;
        $user->is_expenses_new      = $is_expenses_new;
        $user->password             = $npass;
        $user->save();

        toastr()->success('تم حفظ المعلومات بنجاح');
        return back();
    }
    public function newusers()
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        return view('admin.newusers');
    }
    public function usersStore(Request $request)
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        $id = \Auth::user()->id;
        $validated = $request->validate(
            [
                'name'              => 'required',
                'mobile_number'     => 'required|unique:users',
                'username'          => 'required|unique:users',
                'rule_name'         => 'required',
                'password'          => 'required|confirmed',
            ],
            [
                'name.required'                     => 'يرجى كتابة الاسم',
                'mobile_number.required'            => 'يرجى كتابة رقم الهاتف',
                'mobile_number.unique'              => 'رقم الهاتف مسجل مسبقاً',
                'username.required'                 => 'يرجى كتابة اسم المستخدم',
                'username.unique'                   => 'اسم المستخدم مسجل مسبقاً',
                'rule_name.required'                => 'يرجى اختيار صلاحية المستخدم',
                'password.required'                 => 'يرجى ادخال كلمة السر',
                'password.confirmed'                => 'كلمة السر غير متطابقة!',
            ]
        );
        $is_deposits = '0';
        $is_deposits_new = '0';
        $is_expenses = '0';
        $is_expenses_new = '0';

        if ($request->rule_name == 'admin') {
            $rule = 'مسؤول النظام';
            $is_deposits = '1';
            $is_deposits_new = '1';
            $is_expenses = '1';
            $is_expenses_new = '1';
        } elseif ($request->rule_name == 'user1') {
            $rule = 'الحسابات';
        } elseif ($request->rule_name == 'user2') {
            $rule = 'المبيعات';
        } elseif ($request->rule_name == 'user3') {
            $rule = 'القانونية';
        } elseif ($request->rule_name == 'user4') {
            $rule = 'المراقب';
            $is_deposits = '1';
            $is_deposits_new = '0';
            $is_expenses = '1';
            $is_expenses_new = '0';
        } elseif ($request->rule_name == 'user5') {
            $rule = 'حسابات ومبيعات';
            $is_deposits = '1';
            $is_deposits_new = '1';
            $is_expenses = '1';
            $is_expenses_new = '1';
        } elseif ($request->rule_name == 'user6') {
            $rule = 'مشروع';
        }
        if ($request->rule_name == 'user1') {
            if ($request->is_deposits == '1') {
                $is_deposits = '1';
            }
            if ($request->is_deposits_new == '1') {
                $is_deposits_new = '1';
            }
            if ($request->is_expenses == '1') {
                $is_expenses = '1';
            }
            if ($request->is_expenses_new == '1') {
                $is_expenses_new = '1';
            }
        }
        User::create([
            'name'              => $request->name,
            'mobile_number'     => $request->mobile_number,
            'username'          => $request->username,
            'password'          => Hash::make($request->password),
            'rules'             => 'admin',
            'rule'              => $request->rule_name,
            'rule_name'         => $rule,
            'user_id'           => $id,
            'is_deposits'       => $is_deposits,
            'is_deposits_new'   => $is_deposits_new,
            'is_expenses'       => $is_expenses,
            'is_expenses_new'   => $is_expenses_new,
        ]);
        toastr()->success('تم اضافة المستخدم بنجاح');
        return back();
    }
    // Profile
    public function deleteuser(Request $request)
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        User::find($request->id)->delete();
        toastr()->success('تم حذف المستخدم بنجاح');
        return back();
    }
    public function doors(Request $request)
    {

        if ($request->ajax()) {
            $output = '';
            $output2 = '';
            $output3 = '';
            $output4 = '';

            $data = DB::select('select * from doors where type = ? order by id desc', [1]);
            $data2 = DB::select('select * from doors where type = ? order by id desc', [2]);

            // Type 1
            $output .= '<table class="table">
            <thead>
              <tr>
                <th scope="col">#</th>
                <th scope="col">اسم الباب</th>
                <th>حذف</th>
              </tr>
            </thead>
            <tbody>';
            foreach ($data as $key => $value) {
                $output .= '<tr>
                <th scope="row">' .  ($key + 1) . '</th>
                <td>' . $value->name . '</td>
                <td>
                <a href="#" class="deletedoor" data-url-delete-door="' . route('doors.destroydoors') . '" id="' . $value->id . '"><i class="fa-solid fa-trash text-danger"></i></a>
                <a href="#" class="editdoor" data-url-edit-door="" data-door-name="' . $value->name . '" data-door-type="' . $value->type . '" id="' . $value->id . '"><i class="fa-solid fa-edit text-primary"></i></a>
                </td>
              </tr>';
            }
            $output .= '</tbody>
          </table>';

            // Type 2
            $output2 .= '<table class="table">
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col">اسم الباب</th>
            <th>حذف</th>
          </tr>
        </thead>
        <tbody>';
            foreach ($data2 as $key => $value) {
                $output2 .= '<tr>
            <th scope="row">' . ($key + 1) . '</th>
            <td>' . $value->name . '</td>
            <td>
            <a class="deletedoor" data-url-delete-door="' . route('doors.destroydoors') . '" href="#" id="' . $value->id . '"><i class="fa-solid fa-trash text-danger"></i></a>
            <a href="#" class="editdoor" data-url-edit-door="" data-door-name="' . $value->name . '" data-door-type="' . $value->type . '" id="' . $value->id . '"><i class="fa-solid fa-edit text-primary"></i></a>
            </td>
          </tr>';
            }
            $output2 .= '</tbody>
      </table>';

            foreach ($data as $key => $value) {
                $output3 .= '<option value="' . $value->id . '">' . $value->name . '</option>';
            }

            foreach ($data2 as $key => $value) {
                $output4 .=  '<option value="' . $value->id . '">' . $value->name . '</option>';
            }
            return response()->json([
                'table_data_type1' => $output,
                'table_data_type2' => $output2,
                'select_data_type1' => $output3,
                'select_data_type2' => $output4,
            ]);
        }
    }
    public function storedoors(Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4') {
            abort(404);
        }
        if (\Auth::user()->is_deposits == 0) {
            abort(404);
        }
        if (\Auth::user()->is_deposits_new == 0) {
            abort(404);
        }
        if (\Auth::user()->is_expenses == 0) {
            abort(404);
        }
        if (\Auth::user()->is_expenses_new == 0) {
            abort(404);
        }
        if ($request->ajax()) {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'type' => 'required',
            ]);

            if ($validator->passes()) {

                if ($request->doorid) {
                    $check = DB::select('select * from doors where name = ? and type = ? and id != ?', [$request->name, $request->type, $request->doorid]);
                    if ($check) {
                        return response()->json([
                            'state' => 'error',
                        ]);
                    } else {
                        DB::table('doors')
                            ->where('id', $request->doorid)
                            ->update([
                                'name' => $request->name,
                                'type' => $request->type
                            ]);
                    }
                } else {
                    $check = DB::select('select * from doors where name = ? and type = ?', [$request->name, $request->type]);
                    if ($check) {
                        return response()->json([
                            'state' => 'error',
                        ]);
                    } else {
                        DB::table('doors')->insert([
                            'name' => $request->name,
                            'type' => $request->type
                        ]);
                    }
                }
                return response()->json([
                    'state' => 'success'
                ]);
            }
        }
    }
    public function destroydoors(Request $request)
    {
        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        if ($request->ajax()) {
            $deleted = DB::delete('delete from doors where id = ? AND id != 1', [$request->id]);
        }
    }
    public function amounts(Request $request)
    {
        if ($request->ajax()) {
            $deposit = Deposit::where('isDelete', 0)->where('wallet', 0)->get();
            $expense = Expense::where('isDelete', 0)->get();
            $project = DB::table('project_deposits_expenses')
                ->where('isDelete', 0)
                ->get();
            $sum1 = 0;
            $sum2 = 0;
            $sum3 = 0;
            $sum4 = 0;
            $sum5 = 0;
            foreach ($deposit as $key => $value) {
                $sum1 += str_replace(',', '', $value->deposit_amount);
                if ($value->deposit_door == 'عملاء') {
                    $sum3 += str_replace(',', '', $value->deposit_amount);
                }
            }
            foreach ($expense as $key => $value) {
                $sum2 += str_replace(',', '', $value->expense_amount);
            }
            foreach ($project as $key => $value) {
                if ($value->type == '1') {
                    $sum4 += str_replace(',', '', $value->amount);
                }
                if ($value->type == '2') {
                    $sum5 += str_replace(',', '', $value->amount);
                }
            }
            $total = $sum1 - $sum2;
            $total2 = $sum4 - $sum5;
            return response()->json([
                'deposit' => number_format($sum1, 0),
                'expense' => number_format($sum2, 0),
                'total' => number_format($total, 0),
                'customers' => number_format($sum3, 0),
                'project1' => number_format($sum4, 0),
                'project2' => number_format($sum5, 0),
                'project3' => number_format($total2, 0),
            ]);
        }
    }
    public function logs(Request $request)
    {
        $type = $request->type;
        $id = $request->id;
        $html = '';
        if ($type == '1') {
            $where = 'client_id';
        } elseif ($type == '2') {
            $where = 'deposit_id';
        } elseif ($type == '3') {
            $where = 'expense_id';
        }
        $logs = DB::table('logs')->where($where, $id)->orderBy('id', 'desc')->get();

        foreach ($logs as $key => $value) {
            if ($value->type == 'اضافة') {
                $class = 'list-group-item-primary';
            } elseif ($value->type == 'تحديث') {
                $class = 'list-group-item-info';
            } elseif ($value->type == 'حذف') {
                $class = 'list-group-item-danger';
            } elseif ($value->type == 'ارجاع') {
                $class = 'list-group-item-success';
            } else {
                $class = 'list-group-item-dark';
            }
            $html .= '<li class="list-group-item ' . $class . '">' . $value->action . ' <small class="text-body-secondary">' . date('Y-m-d h:i a', strtotime($value->created_at)) . '</small></li>';
        }
        if (count($logs) == 0) {
            $html .= '<li class="list-group-item list-group-item-dark">لا يوجد سجلات</li>';
        }
        return $html;
    }
}
