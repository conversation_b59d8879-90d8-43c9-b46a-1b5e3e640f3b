@extends('admin.project.layouts.app2')

@section('title', 'مجمع النجوم السكني')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">

                </div>
                <div class="card" data-aos="fade-up" data-aos-delay="800">
                    <div class="flex-wrap card-header d-flex justify-content-between align-items-center">
                        <div class="header-title">
                            <h6 class="card-title">المخطط البياني للايداعات والصرفيات</h6>
                        </div>
                        <div class="d-flex align-items-center justify-content-between align-self-center">

                            <div class="d-flex align-items-center text-primary">
                                <svg class="icon-12" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24"
                                    fill="currentColor">
                                    <g>
                                        <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                    </g>
                                </svg>
                                <div class="ms-2">
                                    <span class="text-gray">الايداع</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center ms-3 text-info">
                                <svg class="icon-12" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24"
                                    fill="currentColor">
                                    <g>
                                        <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                    </g>
                                </svg>
                                <div class="ms-2">
                                    <span class="text-gray">الصرف</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="d-main2" class="d-main"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-12 col-lg-4">
    <input type="hidden" value="{{route('amounts')}}" id="getAmountUrl">
    <div class="row">
        <div class="col-md-12 col-lg-12">
            <div class="card credit-card-widget" data-aos="fade-up" data-aos-delay="900"
                style="background-color: #e6ddd8">

                <div class="pb-4 border-0 card-header credit-card-widget-hidden-number">
                    <div class="p-4 border border-white rounded primary-gradient-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-center">
                                <h6 class="mb-1">الصندوق</h6>
                                <h6 class="mb-3 hiddenNumber totalAmount"></h6>
                                <h6 class="mb-1">مجموع الايداع الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalDeposits"></h6>
                                <h6 class="mb-1">مجموع الصرف الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalExpenses"></h6>
                            </div>
                            <div class="master-card-content">
                                <img src="{{ asset('public/assets/images/iqd.webp') }}" alt="logo" width="100">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body text-center">
                    <img src="{{ asset('public/assets/images/download.gif') }}" alt="logo" width="260">
                </div>
            </div>
        </div>
    </div>
</div>
@endsection