<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title')</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('public/assets/images/logo2.webp')}}">

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/core/libs.min.css')}}">

    <!-- Aos Animation Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/aos/dist/aos.css')}}">

    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/hope-ui.min.css?v=4.0.0')}}">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/custom.min.css?v=4.0.0')}}">

    <!-- Dark Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/dark.min.css')}}">

    <!-- Customizer Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/customizer.min.css')}}">

    <!-- RTL Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/rtl.min.css')}}">

    <!-- Toasty Notification Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/jquery-confirm-v3.3.4/jquery-confirm.min.css')}}">

    <!-- Select2 Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/select2/dist/css/select2.min.css')}}">

    <!-- Select2 Bootstrap Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/select2/dist/css/select2-bootstrap4.min.css')}}">

    <!-- Fontawesome -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/fontawesome-free-6.6.0/css/all.min.css')}}">

    <!-- Jquery Toast Plugin Master -->
    <link rel="stylesheet"
        href="{{ asset('public/assets/vendor/jquery-toast-plugin-master/dist/jquery.toast.min.css')}}">

    <!-- Style Css -->
    <link rel="stylesheet" href="{{ asset('public/assets/css/style.css')}}">
</head>

<body class="  ">
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->
    @include('admin.include.sidebar')

    <main class="main-content">
        <div class="position-relative iq-banner">
            <!--Nav Start-->
            <nav class="nav navbar navbar-expand-lg navbar-light iq-navbar">
                <div class="container-fluid navbar-inner">
                    <a href="../dashboard/index.html" class="navbar-brand">
                        <!--Logo start-->
                        <div class="logo-main">
                            <div class="logo-normal">
                                <svg class="text-primary icon-30" viewBox="0 0 30 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2"
                                        transform="rotate(-45 -0.757324 19.2427)" fill="currentColor" />
                                    <rect x="7.72803" y="27.728" width="28" height="4" rx="2"
                                        transform="rotate(-45 7.72803 27.728)" fill="currentColor" />
                                    <rect x="10.5366" y="16.3945" width="16" height="4" rx="2"
                                        transform="rotate(45 10.5366 16.3945)" fill="currentColor" />
                                    <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2"
                                        transform="rotate(45 10.5562 -0.556152)" fill="currentColor" />
                                </svg>
                            </div>
                            <div class="logo-mini">
                                <svg class="text-primary icon-30" viewBox="0 0 30 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2"
                                        transform="rotate(-45 -0.757324 19.2427)" fill="currentColor" />
                                    <rect x="7.72803" y="27.728" width="28" height="4" rx="2"
                                        transform="rotate(-45 7.72803 27.728)" fill="currentColor" />
                                    <rect x="10.5366" y="16.3945" width="16" height="4" rx="2"
                                        transform="rotate(45 10.5366 16.3945)" fill="currentColor" />
                                    <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2"
                                        transform="rotate(45 10.5562 -0.556152)" fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <!--logo End-->

                        <h4 class="logo-title">مجمع النجوم السكني</h4>
                    </a>
                    <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
                        <i class="icon">
                            <svg width="20px" class="icon-20" viewBox="0 0 24 24">
                                <path fill="currentColor"
                                    d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z" />
                            </svg>
                        </i>
                    </div>
                    <div>
                        <h5>
                            نظام ادارة مجمع النجوم السكني
                        </h5>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon">
                            <span class="mt-2 navbar-toggler-bar bar1"></span>
                            <span class="navbar-toggler-bar bar2"></span>
                            <span class="navbar-toggler-bar bar3"></span>
                        </span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="mb-2 navbar-nav ms-auto align-items-center navbar-list mb-lg-0">
                            <li class="nav-item dropdown">
                                <a href="#" onclick="forceReload();" title="تحديث الصفحة CTRL + F5" class="nav-link"
                                    id="notification-drop" data-bs-toggle="dropdown">
                                    <i class="fa-solid fa-rotate"></i>
                                    <span class="bg-danger dots"></span>
                                </a>
                            </li>
                            @php
                            $notifaction =
                            count(Illuminate\Support\Facades\DB::table('installments')->where('price_date',
                            '<=', date("Y-m-d"))->where('status',
                                '0')->get());
                                @endphp
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link" id="notification-drop" data-bs-toggle="dropdown"
                                        style="{{$notifaction > 0 ? 'color: #f7bd2d;' : ''}}">
                                        <svg class="icon-24" width="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg" id="{{$notifaction> 0 ? 'bell' : ''}}">
                                            <path
                                                d="M19.7695 11.6453C19.039 10.7923 18.7071 10.0531 18.7071 8.79716V8.37013C18.7071 6.73354 18.3304 5.67907 17.5115 4.62459C16.2493 2.98699 14.1244 2 12.0442 2H11.9558C9.91935 2 7.86106 2.94167 6.577 4.5128C5.71333 5.58842 5.29293 6.68822 5.29293 8.37013V8.79716C5.29293 10.0531 4.98284 10.7923 4.23049 11.6453C3.67691 12.2738 3.5 13.0815 3.5 13.9557C3.5 14.8309 3.78723 15.6598 4.36367 16.3336C5.11602 17.1413 6.17846 17.6569 7.26375 17.7466C8.83505 17.9258 10.4063 17.9933 12.0005 17.9933C13.5937 17.9933 15.165 17.8805 16.7372 17.7466C17.8215 17.6569 18.884 17.1413 19.6363 16.3336C20.2118 15.6598 20.5 14.8309 20.5 13.9557C20.5 13.0815 20.3231 12.2738 19.7695 11.6453Z"
                                                fill="currentColor"></path>
                                            <path opacity="0.4"
                                                d="M14.0088 19.2283C13.5088 19.1215 10.4627 19.1215 9.96275 19.2283C9.53539 19.327 9.07324 19.5566 9.07324 20.0602C9.09809 20.5406 9.37935 20.9646 9.76895 21.2335L9.76795 21.2345C10.2718 21.6273 10.8632 21.877 11.4824 21.9667C11.8123 22.012 12.1482 22.01 12.4901 21.9667C13.1083 21.877 13.6997 21.6273 14.2036 21.2345L14.2026 21.2335C14.5922 20.9646 14.8734 20.5406 14.8983 20.0602C14.8983 19.5566 14.4361 19.327 14.0088 19.2283Z"
                                                fill="currentColor"></path>
                                        </svg>
                                        <span class="bg-danger dots"></span>
                                    </a>
                                    <div class="p-0 sub-drop dropdown-menu dropdown-menu-end"
                                        aria-labelledby="notification-drop">
                                        <div class="m-0 shadow-none card">
                                            <div class="py-3 card-header d-flex justify-content-between bg-primary">
                                                <div class="header-title">
                                                    <h5 class="mb-0 text-white">الاشعارات</h5>
                                                </div>
                                            </div>
                                            <div class="p-0 card-body">
                                                <a href="#tablelateclient" class="iq-sub-card">
                                                    <div class="d-flex align-items-center">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src="{{ asset('public/assets/images/logo2.webp') }}"
                                                            alt="" />
                                                        <div class="ms-3 w-100">
                                                            <h6 class="mb-0 text-start">النظام</h6>
                                                            <div
                                                                class="d-flex justify-content-between align-items-center">
                                                                <small class="float-end font-size-12"> مستحقين
                                                                    التسديد
                                                                    ({{$notifaction}})</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <!--<li class="nav-item dropdown">
                                <a href="#" class="nav-link" id="mail-drop" data-bs-toggle="dropdown"
                                    aria-haspopup="true" aria-expanded="false">
                                    <svg class="icon-24" width="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.4"
                                            d="M22 15.94C22 18.73 19.76 20.99 16.97 21H16.96H7.05C4.27 21 2 18.75 2 15.96V15.95C2 15.95 2.006 11.524 2.014 9.298C2.015 8.88 2.495 8.646 2.822 8.906C5.198 10.791 9.447 14.228 9.5 14.273C10.21 14.842 11.11 15.163 12.03 15.163C12.95 15.163 13.85 14.842 14.56 14.262C14.613 14.227 18.767 10.893 21.179 8.977C21.507 8.716 21.989 8.95 21.99 9.367C22 11.576 22 15.94 22 15.94Z"
                                            fill="currentColor"></path>
                                        <path
                                            d="M21.4759 5.67351C20.6099 4.04151 18.9059 2.99951 17.0299 2.99951H7.04988C5.17388 2.99951 3.46988 4.04151 2.60388 5.67351C2.40988 6.03851 2.50188 6.49351 2.82488 6.75151L10.2499 12.6905C10.7699 13.1105 11.3999 13.3195 12.0299 13.3195C12.0339 13.3195 12.0369 13.3195 12.0399 13.3195C12.0429 13.3195 12.0469 13.3195 12.0499 13.3195C12.6799 13.3195 13.3099 13.1105 13.8299 12.6905L21.2549 6.75151C21.5779 6.49351 21.6699 6.03851 21.4759 5.67351Z"
                                            fill="currentColor"></path>
                                    </svg>
                                    <span class="bg-primary count-mail"></span>
                                </a>
                                <div class="p-0 sub-drop dropdown-menu dropdown-menu-end" aria-labelledby="mail-drop">
                                    <div class="m-0 shadow-none card">
                                        <div class="py-3 card-header d-flex justify-content-between bg-primary">
                                            <div class="header-title">
                                                <h6 class="mb-0 text-white">الرسائل</h6>
                                            </div>
                                        </div>
                                        <div class="p-0 card-body">
                                            <a href="#" class="iq-sub-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src=""
                                                            alt="" />
                                                    </div>
                                                    <div class="ms-3">
                                                        <h6 class="mb-0 text-start">تجريبي</h6>
                                                        <small class="float-start font-size-12">13 Jun</small>
                                                    </div>
                                                </div>
                                            </a>
                                            {{-- <a href="#" class="iq-sub-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src="{{ asset('public/assets/images/shapes/02.png') }}"
                                                            alt="" />
                                                    </div>
                                                    <div class="ms-3">
                                                        <h6 class="mb-0">Lorem Ipsum Watson</h6>
                                                        <small class="float-start font-size-12">20 Apr</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="iq-sub-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src="{{ asset('public/assets/images/shapes/03.png') }}"
                                                            alt="" />
                                                    </div>
                                                    <div class="ms-3">
                                                        <h6 class="mb-0">Why do we use it?</h6>
                                                        <small class="float-start font-size-12">30 Jun</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="iq-sub-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src="{{ asset('public/assets/images/shapes/04.png') }}"
                                                            alt="" />
                                                    </div>
                                                    <div class="ms-3">
                                                        <h6 class="mb-0">Variations Passages</h6>
                                                        <small class="float-start font-size-12">12 Sep</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="iq-sub-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="">
                                                        <img class="p-1 avatar-40 rounded-pill bg-soft-primary"
                                                            src="{{ asset('public/assets/images/shapes/05.png') }}"
                                                            alt="" />
                                                    </div>
                                                    <div class="ms-3">
                                                        <h6 class="mb-0">Lorem Ipsum generators</h6>
                                                        <small class="float-start font-size-12">5 Dec</small>
                                                    </div>
                                                </div>
                                            </a> --}}
                                        </div>
                                    </div>
                                </div>
                            </li>-->
                                <li class="nav-item dropdown">
                                    <a class="py-0 nav-link d-flex align-items-center" href="#" id="navbarDropdown"
                                        role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <img src="{{ asset('public/assets/images/avatars/01.png') }}" alt="User-Profile"
                                            class="theme-color-default-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_1.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-purple-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_2.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-blue-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_4.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-green-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_5.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-yellow-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <img src="{{ asset('public/assets/images/avatars/avtar_3.png') }}"
                                            alt="User-Profile"
                                            class="theme-color-pink-img img-fluid avatar avatar-50 avatar-rounded" />
                                        <div class="caption ms-3 d-none d-md-block">
                                            <h6 class="mb-0 caption-title">{{ Auth::user()->name }}</h6>
                                            <p class="mb-0 caption-sub-title">
                                                {{ Auth::user()->rule_name }}
                                            </p>
                                        </div>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                        <li
                                            style="{{ request()->route()->named('admin.profile') ? 'background: #3a57e8;' : '' }}">
                                            <a class="dropdown-item {{ request()->route()->named('admin.profile') ? 'text-white' : '' }}"
                                                href="{{route('admin.profile')}}">الملف
                                                الشخصي</a>
                                        </li>
                                        <li>
                                            <hr class="dropdown-divider" />
                                        </li>
                                        <li>

                                            <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                                      document.getElementById('logout-form').submit();">
                                                تسجيل الخروج
                                            </a>

                                            <form id="logout-form" action="{{ route('logout') }}" method="POST"
                                                class="d-none">
                                                @csrf
                                            </form>
                                        </li>
                                    </ul>
                                </li>
                        </ul>
                    </div>
                </div>
            </nav>
            <!-- Nav Header Component Start -->
            <div class="iq-navbar-header" style="height: 140px">
                <div class="container-fluid iq-container">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="flex-wrap d-flex justify-content-between align-items-center">
                                <div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="iq-header-img">
                    {{-- <img src="{{ asset('public/assets/images/dashboard/top-header.png') }}" alt="header"
                        class="theme-color-default-img img-fluid w-100 h-100 animated-scaleX" />
                    <img src="{{ asset('public/assets/images/dashboard/top-header1.png') }}" alt="header"
                        class="theme-color-purple-img img-fluid w-100 h-100 animated-scaleX" />
                    <img src="{{ asset('public/assets/images/dashboard/top-header2.png') }}" alt="header"
                        class="theme-color-blue-img img-fluid w-100 h-100 animated-scaleX" />
                    <img src="{{ asset('public/assets/images/dashboard/top-header3.png') }}" alt="header"
                        class="theme-color-green-img img-fluid w-100 h-100 animated-scaleX" />
                    <img src="{{ asset('public/assets/images/dashboard/top-header4.png') }}" alt="header"
                        class="theme-color-yellow-img img-fluid w-100 h-100 animated-scaleX" />
                    <img src="{{ asset('public/assets/images/dashboard/top-header5.png') }}" alt="header"
                        class="theme-color-pink-img img-fluid w-100 h-100 animated-scaleX" /> --}}
                </div>
            </div>
            <!-- Nav Header Component End -->
            <!--Nav End-->
        </div>
        <div class="conatiner-fluid content-inner mt-n5 py-0">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <div class="row row-cols-1">
                        <div class="overflow-hidden d-slider1 ">
                            <ul class="p-0 m-0 swiper-wrapper list-inline" style="margin-bottom: .1rem !important;">
                                @if (\Auth::user()->rule == 'user1' || \Auth::user()->rule == 'user4' ||
                                \Auth::user()->rule == 'admin' ||
                                \Auth::user()->rule == 'user5')
                                @if (\Auth::user()->is_deposits == '1')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="700"
                                    style="{{ request()->route()->named('deposits.show') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('deposits.show')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/donation.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('deposits.show') ? 'color: #fff!important;' : '' }}">
                                                        دفتر الايداع</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @if (\Auth::user()->rule != 'user4')
                                @if (\Auth::user()->is_deposits_new == '1')
                                <li class="swiper-slide card card-slide"
                                    style="{{ request()->route()->named('deposits.create') ? 'background-color: #1c7b7c!important;' : '' }}"
                                    data-aos="fade-up" data-aos-delay="800">
                                    <a href="{{route('deposits.create')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/wallet.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('deposits.create') ? 'color: #fff!important;' : '' }}">
                                                        ايداع جديد</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @endif
                                @if (\Auth::user()->is_expenses == '1')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('expense.show') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('expense.show')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/expense.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('expense.show') ? 'color: #fff!important;' : '' }}">
                                                        دفتر الصرف</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @if (\Auth::user()->rule != 'user4')
                                @if (\Auth::user()->is_expenses_new == '1')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('expense.create') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('expense.create')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/capital.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('expense.create') ? 'color: #fff!important;' : '' }}">
                                                        صرف جديد</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @endif
                                @endif
                                @if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user4' ||
                                \Auth::user()->rule == 'admin' ||
                                \Auth::user()->rule == 'user5')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('clients') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('clients')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/customer.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('clients') ? 'color: #fff!important;' : '' }}">
                                                        العملاء</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'admin' ||
                                \Auth::user()->rule == 'user5')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('clients.new') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('clients.new')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <img src="{{ asset('public/assets/images/icons/user.png') }}"
                                                        alt="logo" width="30">
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('clients.new') ? 'color: #fff!important;' : '' }}">
                                                        عميل جديد</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                                @if (\Auth::user()->rule == 'user4')
                                <li class="swiper-slide card card-slide" data-aos="fade-up" data-aos-delay="900"
                                    style="{{ request()->route()->named('contracts') ? 'background-color: #1c7b7c!important;' : '' }}">
                                    <a href="{{route('contracts')}}">
                                        <div class="card-body">
                                            <div class="progress-widget">
                                                <div>
                                                    <i class="icon">
                                                        <svg class="icon-30" width="30" viewBox="0 0 24 24" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                                d="M7.81 2H16.191C19.28 2 21 3.78 21 6.83V17.16C21 20.26 19.28 22 16.191 22H7.81C4.77 22 3 20.26 3 17.16V6.83C3 3.78 4.77 2 7.81 2ZM8.08 6.66V6.65H11.069C11.5 6.65 11.85 7 11.85 7.429C11.85 7.87 11.5 8.22 11.069 8.22H8.08C7.649 8.22 7.3 7.87 7.3 7.44C7.3 7.01 7.649 6.66 8.08 6.66ZM8.08 12.74H15.92C16.35 12.74 16.7 12.39 16.7 11.96C16.7 11.53 16.35 11.179 15.92 11.179H8.08C7.649 11.179 7.3 11.53 7.3 11.96C7.3 12.39 7.649 12.74 8.08 12.74ZM8.08 17.31H15.92C16.319 17.27 16.62 16.929 16.62 16.53C16.62 16.12 16.319 15.78 15.92 15.74H8.08C7.78 15.71 7.49 15.85 7.33 16.11C7.17 16.36 7.17 16.69 7.33 16.95C7.49 17.2 7.78 17.35 8.08 17.31Z"
                                                                fill="currentColor"></path>
                                                        </svg>
                                                    </i>
                                                </div>
                                                <div class="progress-detail">
                                                    <p
                                                        style="{{ request()->route()->named('contracts') ? 'color: #fff!important;' : '' }}">
                                                        العقود</p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
                @yield('content')
            </div>
        </div>
        <!-- Footer Section Start -->
        <footer class="footer">
            <div class="footer-body">
                <div class="lefts-panel">
                    ©
                    <script>
                        document.write(new Date().getFullYear());
                    </script>
                    SEIO, Made with
                    <span class="">
                        <svg class="icon-15" width="15" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M15.85 2.50065C16.481 2.50065 17.111 2.58965 17.71 2.79065C21.401 3.99065 22.731 8.04065 21.62 11.5806C20.99 13.3896 19.96 15.0406 18.611 16.3896C16.68 18.2596 14.561 19.9196 12.28 21.3496L12.03 21.5006L11.77 21.3396C9.48102 19.9196 7.35002 18.2596 5.40102 16.3796C4.06102 15.0306 3.03002 13.3896 2.39002 11.5806C1.26002 8.04065 2.59002 3.99065 6.32102 2.76965C6.61102 2.66965 6.91002 2.59965 7.21002 2.56065H7.33002C7.61102 2.51965 7.89002 2.50065 8.17002 2.50065H8.28002C8.91002 2.51965 9.52002 2.62965 10.111 2.83065H10.17C10.21 2.84965 10.24 2.87065 10.26 2.88965C10.481 2.96065 10.69 3.04065 10.89 3.15065L11.27 3.32065C11.3618 3.36962 11.4649 3.44445 11.554 3.50912C11.6104 3.55009 11.6612 3.58699 11.7 3.61065C11.7163 3.62028 11.7329 3.62996 11.7496 3.63972C11.8354 3.68977 11.9247 3.74191 12 3.79965C13.111 2.95065 14.46 2.49065 15.85 2.50065ZM18.51 9.70065C18.92 9.68965 19.27 9.36065 19.3 8.93965V8.82065C19.33 7.41965 18.481 6.15065 17.19 5.66065C16.78 5.51965 16.33 5.74065 16.18 6.16065C16.04 6.58065 16.26 7.04065 16.68 7.18965C17.321 7.42965 17.75 8.06065 17.75 8.75965V8.79065C17.731 9.01965 17.8 9.24065 17.94 9.41065C18.08 9.58065 18.29 9.67965 18.51 9.70065Z"
                                fill="currentColor"></path>
                        </svg>
                    </span>
                    by <a href="https://seio.uk/" target="_blank">Scientific Engineering Information Office</a>
                </div>
            </div>
        </footer>
        <!-- Footer Section End -->
    </main>

    @include('admin.include.modal')
    <!-- Wrapper End-->
    <!-- Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/libs.min.js')}}"></script>

    <!-- External Library Bundle Script -->
    <script src="{{ asset('public/assets/js/core/external.min.js')}}"></script>

    <!-- Widgetchart Script -->
    <script src="{{ asset('public/assets/js/charts/widgetcharts.js')}}"></script>

    <!-- mapchart Script -->
    <script src="{{ asset('public/assets/js/charts/vectore-chart.js')}}"></script>
    <script src="{{ asset('public/assets/js/charts/dashboard.js')}}"></script>

    <!-- fslightbox Script -->
    <script src="{{ asset('public/assets/js/plugins/fslightbox.js')}}"></script>

    <!-- Settings Script -->
    <script src="{{ asset('public/assets/js/plugins/setting.js')}}"></script>

    <!-- Slider-tab Script -->
    <script src="{{ asset('public/assets/js/plugins/slider-tabs.js')}}"></script>

    <!-- Form Wizard Script -->
    <script src="{{ asset('public/assets/js/plugins/form-wizard.js')}}"></script>

    <!-- AOS Animation Plugin-->
    <script src="{{ asset('public/assets/vendor/aos/dist/aos.js')}}"></script>

    <!-- Toasty Notification Js -->
    <script src="{{ asset('public/assets/vendor/jquery-confirm-v3.3.4/jquery-confirm.min.js')}}"></script>

    <!-- App Script -->
    <script src="{{ asset('public/assets/js/hope-ui.js')}}" defer></script>

    <!-- Jquery Toast Plugin Master -->
    <script src="{{ asset('public/assets/vendor/jquery-toast-plugin-master/dist/jquery.toast.min.js')}}" defer></script>

    <!-- number2string Script -->
    <script src="{{ asset('public/assets/js/number2string.js')}}"></script>

    <!-- Fontawesome -->
    <link rel="stylesheet" href="{{ asset('public/assets/vendor/fontawesome-free-6.6.0/js/all.min.js')}}">

    <!-- Select2 -->
    <script src="{{ asset('public/assets/vendor/select2/dist/js/select2.full.min.js')}}"></script>

    @if (request()->route()->named('deposits.show') || request()->route()->named('expense.show'))
    <!-- Style Script -->
    <script src="{{ asset('public/assets/js/search.js')}}"></script>
    @endif
    @if (request()->route()->named('clients') ||
    request()->route()->named('contracts'))
    <!-- Style Script -->
    <script src="{{ asset('public/assets/js/searchclient.js')}}"></script>
    @endif
    <!-- Style Script -->
    <script src="{{ asset('public/assets/js/style.js')}}"></script>

    @if (request()->route()->named('index'))
    <!-- Style Script -->
    @if (\Auth::user()->rule == 'admin' || \Auth::user()->rule == 'user4')

    <script>
        if (document.querySelectorAll("#d-main").length) {
            const options = {
                series: [{
                        name: "الايداع",
                        data: [94, 80, 94, 80, 94, 80, 94],
                    },
                    {
                        name: "الصرف",
                        data: [72, 60, 84, 60, 74, 60, 78],
                    },
                ],
                chart: {
                    fontFamily: '"Inter", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    height: 245,
                    type: "area",
                    toolbar: {
                        show: false,
                    },
                    sparkline: {
                        enabled: false,
                    },
                },
                colors: ["#3a57e8", "#4bc7d2"],
                dataLabels: {
                    enabled: false,
                },
                stroke: {
                    curve: "smooth",
                    width: 3,
                },
                yaxis: {
                    show: true,
                    labels: {
                        show: true,
                        minWidth: 19,
                        maxWidth: 19,
                        style: {
                            colors: "#8A92A6",
                        },
                        offsetX: -5,
                    },
                },
                legend: {
                    show: false,
                },
                xaxis: {
                    labels: {
                        minHeight: 22,
                        maxHeight: 22,
                        show: true,
                        style: {
                            colors: "#8A92A6",
                        },
                    },
                    lines: {
                        show: false, //or just here to disable only x axis grids
                    },
                    categories: ["Jan", "Feb", "Mar", "Apr", "Jun", "Jul", "Aug"],
                },
                grid: {
                    show: false,
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "dark",
                        type: "vertical",
                        shadeIntensity: 0,
                        gradientToColors: undefined, // optional, if not defined - uses the shades of same color in series
                        inverseColors: true,
                        opacityFrom: 0.4,
                        opacityTo: 0.1,
                        stops: [0, 50, 80],
                        colors: ["#3a57e8", "#4bc7d2"],
                    },
                },
                tooltip: {
                    enabled: true,
                },
            };

            const chart = new ApexCharts(
                document.querySelector("#d-main"),
                options
            );
            chart.render();
            document.addEventListener("ColorChange", (e) => {
                console.log(e);
                const newOpt = {
                    colors: [e.detail.detail1, e.detail.detail2],
                    fill: {
                        type: "gradient",
                        gradient: {
                            shade: "dark",
                            type: "vertical",
                            shadeIntensity: 0,
                            gradientToColors: [e.detail.detail1, e.detail.detail2], // optional, if not defined - uses the shades of same color in series
                            inverseColors: true,
                            opacityFrom: 0.4,
                            opacityTo: 0.1,
                            stops: [0, 50, 60],
                            colors: [e.detail.detail1, e.detail.detail2],
                        },
                    },
                };
                chart.updateOptions(newOpt);
            });
        }
    </script>
    @endif
    @endif
</body>

</html>