<div class="col-md-12 col-lg-4">
    <input type="hidden" value="{{route('amounts')}}" id="getAmountUrl">
    <div class="row">
        <div class="col-md-12 col-lg-12">
            <div class="card credit-card-widget" data-aos="fade-up" data-aos-delay="900"
                style="background-color: #e6ddd8">
                @if (\Auth::user()->rule == 'user1' || \Auth::user()->rule == 'user4' || \Auth::user()->rule == 'admin'
                || \Auth::user()->rule == 'user5')
                <div class="pb-4 border-0 card-header credit-card-widget-hidden-number">
                    <div class="p-4 border border-white rounded primary-gradient-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-center">
                                <h6 class="mb-1">الصندوق</h6>
                                <h6 class="mb-3 hiddenNumber totalAmount"></h6>
                                <h6 class="mb-1">مجموع الايداع الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalDeposits"></h6>
                                <h6 class="mb-1">مجموع الصرف الكلي</h6>
                                <h6 class="mb-2 hiddenNumber totalExpenses"></h6>
                                <h6 class="mb-1">مجموع ايداع العملاء</h6>
                                <h6 class="hiddenNumber totalCustomers">0</h6>
                            </div>
                            <div class="master-card-content">
                                <img src="{{ asset('public/assets/images/iqd.webp') }}" alt="logo" width="100">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body text-center">
                    <img src="{{ asset('public/assets/images/download.gif') }}" alt="logo" width="260">
                </div>
                @else
                <div class="card-body text-center">
                    <img src="{{ asset('public/assets/images/download.gif') }}" alt="logo" width="260">
                </div>
                @endif
            </div>
        </div>
    </div>
</div>