<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Deposit;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Client;

class DepositController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4' || \Auth::user()->rule == 'user6') {
            abort(404);
        }
        if (\Auth::user()->is_deposits_new == 0) {
            abort(404);
        }
        return view('admin.newdeposit');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4') {
            abort(404);
        }
        if (\Auth::user()->is_deposits_new == 0) {
            abort(404);
        }
        $id = \Auth::user()->id;
        if ($request->ajax()) {
            $validated = $request->validate(
                [
                    'day'                   => 'required',
                    'month'                 => 'required',
                    'year'                  => 'required',
                    'deposit_door'          => 'required',
                    'deposit_name'          => 'required',
                    'deposit_type'          => 'required',
                    'deposit_amount'        => 'required',
                    'deposit_details'       => 'required',
                ],
                [
                    'day.required'                              => 'يرجى اختيار اليوم',
                    'month.required'                            => 'يرجى اختيار الشهر',
                    'year.required'                             => 'يرجى اختيار السنة',
                    'deposit_door.required'                     => 'يرجى اختيار باب الايداع',
                    'deposit_name.required'                     => 'يرجى ادخال المودع',
                    'deposit_type.required'                     => 'يرجى اختيار نوع الايداع',
                    'deposit_amount.required'                   => 'يرجى ادخال مبلغ الايداع',
                    'deposit_details.required'                  => 'يرجى كتابة التفاصيل',
                ]
            );


            if ($request->hasFile('deposit_file')) {
                $allfiles = array();
                $files = $request->file('deposit_file');

                foreach ($files as $key => $file) {
                    if ($file->isValid()) {
                        $filePath = $file->store('uploads/deposit', 'public');
                        $fileName = $filePath;
                        $allfiles[] = $fileName;
                    }
                }
                $empty_array = array($allfiles);
                if (!empty($empty_array)) {
                    $res_file = implode(',', $allfiles);
                } else {
                    $res_file = NULL;
                }
            } else {
                $res_file = NULL;
            }
            $wallet = 0;
            $calc_deposit_name = DB::table('wallets')->where('client_id', $request->deposit_name)->first();
            if ($request->deposit_type == 'اضافة الى المحفظة') {
                $wallet = 0;
                $final_res = number_format(str_replace(',', '', $calc_deposit_name->wallet_price) + str_replace(',', '', $request->deposit_amount), 0);
                DB::table('wallets_actions')->insert([
                    'wallets_id' => $calc_deposit_name->id,
                    'client_id' => $request->deposit_name,
                    'action' => 'add',
                    'price_old' => empty($calc_deposit_name->wallet_price) ? 0 : $calc_deposit_name->wallet_price,
                    'price_new' => $request->deposit_amount,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
                DB::update(
                    'update wallets set wallet_price = ?, updated_at = ? where client_id = ?',
                    [$final_res, date('Y-m-d H:i:s'), $request->deposit_name]
                );
            }
            if ($request->deposit_type == 'سحب من المحفظة') {
                if (str_replace(',', '', $request->deposit_amount) > str_replace(',', '', $calc_deposit_name->wallet_price)) {
                    return;
                }
                $wallet = 1;
                $final_res = number_format(str_replace(',', '', $calc_deposit_name->wallet_price) - str_replace(',', '', $request->deposit_amount), 0);
                DB::table('wallets_actions')->insert([
                    'wallets_id' => $calc_deposit_name->id,
                    'client_id' => $request->deposit_name,
                    'action' => 'withdraw',
                    'price_old' => $calc_deposit_name->wallet_price,
                    'price_new' => $request->deposit_amount,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
                DB::update(
                    'update wallets set wallet_price = ?, updated_at = ? where client_id = ?',
                    [$final_res, date('Y-m-d H:i:s'), $request->deposit_name]
                );
            }
            $depositid = Deposit::create([
                'day'                       => $request->day,
                'month'                     => $request->month,
                'year'                      => $request->year,
                'deposit_date'              => $request->year . '-' . $request->month . '-' . $request->day,
                'deposit_door'              => $request->deposit_door,
                'deposit_door_name'         => $request->deposit_door_name,
                'deposit_name'              => $request->deposit_name,
                'client_name'               => $request->client_name,
                'house_no'                  => $request->house_no,
                'deposit_type'              => $request->deposit_type,
                'deposit_amount'            => $request->deposit_amount,
                'price_name'                => $request->price_name,
                'deposit_details'           => $request->deposit_details,
                'wallet'                    => $wallet,
                'deposit_file'              => $res_file,
                'user_id'                   => $id,
                // 'installments_id'           => $request->installments_id,
            ]);
            if ($request->deposit_type != 'اضافة الى المحفظة') {
                if ($request->installments_id) {
                    foreach ($request->installments_id as $key => $value) {
                        if (!empty($value)) {
                            DB::update(
                                'update installments set status = 1, updated_at = ?, deposits_id = ? where id = ?',
                                [date('Y-m-d H:i:s'), $depositid->id, $value]
                            );
                        }
                    }
                }
            }
            DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $depositid->id, 'اضافة', 'تم اضافة سند الايداع رقم الاي دي ' . $depositid->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);

            return response()->json(['id' => $depositid->id, 'state' => 'success']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */

    public function show(Deposit $deposit, Request $request)
    {

        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3') {
            abort(404);
        }
        if (\Auth::user()->is_deposits == 0) {
            abort(404);
        }
        if ($request->ajax()) {
            $output = '';
            $pagination = 10;

            $data = Deposit::where('isDelete', 0)->where('wallet', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('deposit_door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('client_name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%')
                            ->orWhere('house_no', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%')
                            ->orWhere('deposit_name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '') {
                        return $q->where('deposit_details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') == '') {
                        return $q->where('deposit_date', '=',  $request->get('query4'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') != '') {
                        return $q->where('deposit_date', '>=', $request->get('query4'))
                            ->where('deposit_date', '<=', $request->get('query5'));
                    }
                })
                ->orderBy('deposit_date', 'desc');
            $data3 = $data->get();
            $data2 = $data->paginate($pagination);
            $total_row = $data2->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">التاريخ</th>
                                    <th scope="col">الباب</th>
                                    <th scope="col">المبلغ</th>
                                    <th scope="col">المودع</th>
                                    <th scope="col">التفاصيل</th>
                                    <th scope="col">نوع الايداع</th>
                                    <th scope="col">الادارة</th>
                                </tr>
                            </thead><tbody>';
                foreach ($data3 as $key => $value) {
                    $sum += str_replace(',', '', $value->deposit_amount);
                }
                foreach ($data2 as $row) {
                    $houseno = ($row->deposit_door == 1 ? Client::where('id', $row->deposit_name)->first()->name_purshes . ' | ' . Client::where('id', $row->deposit_name)->first()->house_no : $row->deposit_name);
                    $output .= '<tr>
                                    <td>' . $row->id . '</td>
                                    <td>' . $row->year . '/' . $row->month . '/' . $row->day . '</td>
                                    <td>' . DB::table('doors')->where('id', $row->deposit_door)->first()->name . '</td>
                                    <td>' . $row->deposit_amount . '</td>
                                    <td>' . $houseno . '</td>
                                    <td>' . $row->deposit_details . '</td>
                                    <td>' . $row->deposit_type . '</td>
                                    <td>
                                        <button type="button" class="me-2 btn ' . (!empty($row->deposit_file) ? 'btn-info showfiles' : 'btn-light') . ' btn-sm" id="' . $row->id . '" data-url="' . url('') . '" data-get-files="' . $row->deposit_file . '"
                                         title="الملفات"><i class="fa-solid fa-file"></i></button>';
                    if (\Auth::user()->rule == 'admin') {
                        $output .= '<button type="button" class="btn ' . ($row->deposit_door_name != 'عملاء' ? 'btn-primary edit' : ($row->deposit_type == 'اضافة الى المحفظة' ? (Client::where('id', $row->deposit_name)->whereNull('sold_type')->first() ? 'btn-primary edit' : 'btn-light') : 'btn-light')) . ' btn-sm" data-type-form="1" id="' . $row->id . '" 
                                        data-day="' . $row->day . '" 
                                        data-month="' . $row->month . '" 
                                        data-year="' . $row->year . '" 
                                        data-deposit-name="' . $row->deposit_name . '"
                                        data-client-name="' . $row->client_name . '"
                                        data-client-id="' . $row->deposit_name . '"
                                        data-deposit-type="' . $row->deposit_type . '"
                                        data-deposit-amount="' . $row->deposit_amount . '"
                                        data-deposit-details="' . $row->deposit_details . '"
                                        data-deposit-file="' . $row->deposit_file . '"
                                        deposit-door-name="' . $row->deposit_door_name . '"
                                        deposit-house-no="' . $houseno . '"
                                        ><i
                                                class="fa-solid fa-pen-to-square" title="تعديل"></i></button>

                                        <button type="button" class="btn btn-danger btn-sm delete" 
                                        data-url-delete="' . route('deposits.destroy') . '" 
                                        data-deposit-type="' . $row->deposit_type . '" 
                                        data-deposit-amount="' . $row->deposit_amount . '" 
                                        data-client-id="' . $row->deposit_name . '" 
                                        id="' . $row->id . '" title="حذف"><i
                                                class="fa-solid fa-trash"></i></button>';
                    }
                    if (\Auth::user()->rule == 'admin' || \Auth::user()->rule == 'user4') {
                        $output .= '<button type="button" class="btn btn-warning btn-sm showlogs ms-2" data-logs-type="2" data-url-logs="' . route('logs') . '" id="' . $row->id . '" title="سجلات"><i class="fa-solid fa-circle-question"></i></button>';
                    }
                    $output .= '</td>
                                </tr>';
                }
                $output .= '</tbody></table></div>';
                $output .= $data2->links();
            } else {
                $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
            }
            return response()->json([
                'table_data' => $output,
                'sum' => number_format($sum, 0)
            ]);
        }
        return view('admin.showdeposit');
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if (\Auth::user()->rule == 'user2' || \Auth::user()->rule == 'user3' || \Auth::user()->rule == 'user4') {
            abort(404);
        }
        if (\Auth::user()->is_deposits_new == 0) {
            abort(404);
        }
        if ($request->ajax()) {
            $validated = $request->validate(
                [
                    'day'                   => 'required',
                    'month'                 => 'required',
                    'year'                  => 'required',
                    'deposit_amount'        => 'required',
                    'deposit_details'       => 'required',
                ],
                [
                    'day.required'                              => 'يرجى اختيار اليوم',
                    'month.required'                            => 'يرجى اختيار الشهر',
                    'year.required'                             => 'يرجى اختيار السنة',
                    'deposit_amount.required'                   => 'يرجى ادخال مبلغ الايداع',
                    'deposit_details.required'                  => 'يرجى كتابة التفاصيل',
                ]
            );

            if ($request->hasFile('deposit_file')) {
                $allfiles = array();
                $files = $request->file('deposit_file');

                foreach ($files as $key => $file) {
                    if ($file->isValid()) {
                        $filePath = $file->store('uploads/deposit', 'public');
                        $fileName = $filePath;
                        $allfiles[] = $fileName;
                    }
                }
                $empty_array = array($allfiles);
                if (!empty($empty_array)) {
                    $res_file = implode(',', $allfiles);
                } else {
                    $res_file = $request->deposit_file_old;
                }
            } else {
                $res_file = $request->deposit_file_old;
            }

            if ($request->deposit_type == 'اضافة الى المحفظة' || $request->deposit_door_name != 'عملاء') {
                if ($request->deposit_type == 'اضافة الى المحفظة') {
                    $wallets = DB::table('wallets')
                        ->where('client_id', '=', $request->clientId)
                        ->first();
                    $final_res = str_replace(',', '', $wallets->wallet_price) - str_replace(',', '', $request->old_deposit_amount);
                    $final_res2 = number_format($final_res + str_replace(',', '', $request->deposit_amount), 0);

                    DB::table('wallets_actions')->insert([
                        'wallets_id' => $wallets->id,
                        'client_id' => $request->clientId,
                        'action' => 'edit',
                        'price_old' => $wallets->wallet_price,
                        'price_new' => $request->deposit_amount,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                    DB::update(
                        'update wallets set wallet_price = ?, updated_at = ? where client_id = ?',
                        [$final_res2, date('Y-m-d H:i:s'), $request->clientId]
                    );
                }

                $deposit = Deposit::find($request->id);
                $deposit->day = $request->day;
                $deposit->month = $request->month;
                $deposit->year = $request->year;
                $deposit->deposit_date = $request->year . '-' . $request->month . '-' . $request->day;
                if ($request->deposit_door_name != 'عملاء') {
                    $deposit->deposit_name = $request->deposit_name;
                }
                $deposit->deposit_amount = $request->deposit_amount;
                $deposit->deposit_details = $request->deposit_details;
                $deposit->deposit_file = $res_file;

                $deposit->save();

                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'تحديث', 'تم تحديث سند الايداع رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return response()->json(['state' => 'success']);
            }
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Deposit $deposit)
    {

        if (\Auth::user()->rule != 'admin') {
            abort(404);
        }
        if ($request->ajax()) {
            $installments = DB::table('installments')
                ->where('client_id', '=', $request->clientId)
                ->where('status', '=', 1)
                ->first();
            if ($request->depositType == 'اضافة الى المحفظة') {
                if ($installments) {
                    return 'error';
                } else {
                    $wallets = DB::table('wallets')
                        ->where('client_id', '=', $request->clientId)
                        ->first();
                    $final_res = number_format(str_replace(',', '', $wallets->wallet_price) - str_replace(',', '', $request->depositAmount), 0);
                    DB::table('wallets_actions')->insert([
                        'wallets_id' => $wallets->id,
                        'client_id' => $request->clientId,
                        'action' => 'remove',
                        'price_old' => $wallets->wallet_price,
                        'price_new' => $request->depositAmount,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                    DB::update(
                        'update wallets set wallet_price = ?, updated_at = ? where client_id = ?',
                        [$final_res, date('Y-m-d H:i:s'), $request->clientId]
                    );
                }
            }
            if ($installments) {
                return 'error';
            } else {
                $deposit = Deposit::find($request->id);

                $deposit->isDelete = 1;

                $deposit->save();

                DB::table('installments')
                    ->where('deposits_id', $request->id)
                    ->update(
                        [
                            'status' => 0,
                            'updated_at' => null,
                            'deposits_id' => null,
                        ]
                    );
                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'حذف', 'تم حذف سند الايداع رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return 'success';
            }
        }
    }
    public function printreportdaily(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = Deposit::where('isDelete', 0)->where('wallet', 0)->whereDate('created_at', '=', Carbon::today()->toDateString())->orderBy('id', 'desc')->latest()->get();
            $total_row = $deposit->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<p>التاريخ: ' . Carbon::today()->toDateString() . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">رقم السند</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">المودع</th>
                                <th scope="col">نوع الايداع</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->deposit_door_name . '</td>
                                <td>' . $row->deposit_amount . '</td>
                                <td>' . ($row->deposit_door_name == 'عملاء' ? $row->client_name . ' | ' . $row->house_no : $row->deposit_name) . '</td>
                                <td>' . ($row->deposit_type == 'اضافة الى المحفظة' ? 'محفظة' : 'تسديد') . '</td>
                                <td>' . $row->deposit_details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->deposit_amount != null ? $row->deposit_amount : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="3">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة', 'تم طباعة سند الايداع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
    public function printreport(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = Deposit::where('isDelete', 0)->where('wallet', 0)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('deposit_door', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('client_name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%')
                            ->orWhere('house_no', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '') {
                        return $q->where('deposit_details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query3')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') == '') {
                        return $q->where('deposit_date', '=',  $request->get('query4'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query4') != '' && $request->get('query5') != '') {
                        return $q->where('deposit_date', '>=', $request->get('query4'))
                            ->where('deposit_date', '<=', $request->get('query5'));
                    }
                })
                ->orderBy('id', 'desc')
                ->get();
            $total_row = $deposit->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<p>تم سحب هذا التقرير بتاريخ: ' . Carbon::today()->toDateString() . ' الساعة ' . date('h:i a') . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col" style="width: 56px;">رقم السند</th>
                                <th scope="col" style="width: 66px;">التاريخ</th>
                                <th scope="col">الباب</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">المودع</th>
                                <th scope="col" style="width: 57px;">نوع الايداع</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->deposit_date . '</td>
                                <td>' . $row->deposit_door_name . '</td>
                                <td>' . $row->deposit_amount . '</td>
                                <td>' . ($row->deposit_door_name == 'عملاء' ? $row->client_name . ' | ' . $row->house_no : $row->deposit_name) . '</td>
                                <td>' . ($row->deposit_type == 'اضافة الى المحفظة' ? 'محفظة' : 'تسديد') . '</td>
                                <td>' . $row->deposit_details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->deposit_amount != null ? $row->deposit_amount : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="4">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة حسب الفرز', 'تم طباعة سند الايداع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
}
