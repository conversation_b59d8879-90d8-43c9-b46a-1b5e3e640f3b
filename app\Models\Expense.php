<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Expense extends Model
{
    use HasFactory;

    protected $fillable = [
        'day',
        'month',
        'year',
        'expense_date',
        'expense_door',
        'expense_door_name',
        'expense_name',
        'expense_type',
        'expense_amount',
        'expense_details',
        'expense_file',
        'user_id',
        'isDelete',
    ];
}
