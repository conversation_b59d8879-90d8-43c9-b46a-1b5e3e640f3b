@extends('admin.layouts.app')

@section('title', 'اضافة ايداع جديد')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">
                    <h4 style="font-weight: 700;font-size: 17px;">اضافة ايداع جديد</h4>
                    @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
                <div class="card-body">
                    <form class="newDeposit" data-form-deposit-url="{{route('deposits.store')}}" method="POST"
                        enctype="multipart/form-data">
                        @csrf
                        <label class="form-label" for="input1">تاريخ الايداع</label>
                        <div style="display: flex;align-items: center;">
                            <div class="me-5 ms-3 " id="DivIdToPrint">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input1">اليوم</label>
                                    <select class="form-select" id="input1" style="width: 95px;" name="day" required>
                                        @for ($i = 1; $i <= 31; $i++) <option value="{{$i}}" {{date("d")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="me-5 ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input2">الشهر</label>
                                    <select class="form-select" id="input2" style="width: 95px;" name="month" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 1; $i <= 12; $i++) <option value="{{$i}}" {{date("m")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input3">السنة</label>
                                    <select class="form-select" id="input3" style="width: 150px;" name="year" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 2023; $i <= date("Y"); $i++) <option value="{{$i}}" {{date("Y")==$i
                                            ? 'selected' : '' }}>{{$i}}
                                            </option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-3 row">
                            <div class="col-3">
                                <label class="form-label" for="input4">باب الايداع</label>
                                <select class="form-select" id="input4" data-type-door="1" name="deposit_door" required>

                                </select>
                            </div>
                            <div class="col-9 hiddenInput" style="display: none">
                                <label for="input5" class="form-label">المودع</label>
                                <br>
                                <select class="form-select" id="input5" data-url-fetch="{{route('clients.fetch')}}"
                                    data-url-fetch-installments="{{route('clients.installments.fetch')}}"
                                    data-searchable='searchable' style="display: none">
                                    <option value="">اختر...</option>
                                    @php
                                    $clients = \App\Models\Client::where('isDelete', 0)->orderBy('id', 'desc')->get();
                                    @endphp
                                    @foreach ($clients as $key => $item)
                                    <option value="{{$item->id}}" data-client-name="{{$item->name_purshes}}"
                                        data-house-no="{{$item->house_no}}"
                                        data-client-sold-type="{{$item->sold_type}}">
                                        {{$item->name_purshes .
                                        ' | رقم الدار ('.$item->house_no.')'}}</option>
                                    @endforeach
                                </select>
                                <input class="form-control" type="text" id="input55" style="display: none">
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <div class="col-4 hiddenInput" style="display: none">
                                <label class="form-label" for="input6">نوع الايداع</label>
                                <select class="form-select" id="input6" name="deposit_type" required>

                                </select>
                            </div>
                            <div class="col-4 hiddenInput" style="display: none">
                                <label for="input6666" class="form-label">المحفظة</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1"
                                        style="background-color: #1c7b7c;color: #fff;"><i
                                            class="fa-solid fa-wallet"></i></span>
                                    <input type="text" class="form-control" aria-describedby="basic-addon1" value="0"
                                        disabled id="input6666" style="background-color: #1c7b7c;color: #fff;">
                                </div>
                            </div>
                            <div class="col-4 hiddenInput" style="display: none">
                                <label for="input7" class="form-label">مبلغ الايداع</label>
                                <input class="form-control text-dark" readonly style="font-weight:bold" type="text"
                                    id="input7" name="deposit_amount" data-type="currency" required>
                            </div>
                        </div>
                        <div class="form-group mt-3 row appendinstallments">

                        </div>
                        <div class="form-group mt-3 row">
                            <label for="input8" class="form-label">التفاصيل</label>
                            <input class="form-control" type="text" id="input8" name="deposit_details" required>
                        </div>
                        <div class="d-flex justify-content-between align-items-baseline">
                            <div class="form-group mt-30 " style="width: 60px;">
                                <label for="input9" class="btn btn-custom filupp">
                                    <i class="fa-solid fa-upload"></i>
                                    <input type="file" name="deposit_file[]" multiple class="d-none" value="1"
                                        id="input9" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xlsx" />
                                </label>
                            </div>
                            <div>
                                <span class="filupp-file-name js-value ms-5"></span>
                            </div>
                            <div class="form-group mt-3 text-end">
                                <button type="submit" class="btn btn-success btn-custom btn-send">اضافة</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@include('admin.include.blog1')
@endsection