<!-- Modal -->
<div class="offcanvas offcanvas-start w-50" data-bs-backdrop="static" tabindex="-1" id="doorsStaticBackdrop"
  aria-labelledby="doorsStaticBackdropLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="doorsStaticBackdropLabel">الابواب</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <form id="addDoors" method="post" data-adddoors-url="{{route('doors.storedoors')}}">
      @csrf
      <input type="hidden" name="doorid">
      <div class="mb-3 row">
        <label for="doorname" class="col-sm-2 col-form-label">اسم الباب</label>
        <div class="col-sm-3">
          <input type="text" class="form-control" name="name" id="doorname" required>
        </div>
        <label for="doortype" class="col-sm-2 col-form-label">نوع الباب</label>
        <div class="col-sm-3">
          <select name="type" id="doortype" class="form-control" required>
            <option value="">اختر...</option>
            <option value="1">باب ايداع</option>
            <option value="2">باب صرف</option>
          </select>
        </div>
        <div class="col-sm-2">
          <button type="submit" class="btn btn-primary btn-send">اضافة</button>
        </div>
      </div>
    </form>
    <hr>
    <div id="dynamic_data_doors">
      <div class="row text-center">
        <div class="col-6">
          <p><span class="btn btn-custom">ابواب الايداع</span></p>
          <div id="dynamic_data_doors_type1">

          </div>
        </div>
        <div class="col-6">
          <p><span class="btn btn-custom">ابواب الصرف</span></p>
          <div id="dynamic_data_doors_type2">

          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<input type="hidden" value="{{route('doors.show')}}" id="getDoorsUrl">

<!-- Modal -->
<div class="modal fade" id="showFilesModal" tabindex="-1" aria-labelledby="showFilesModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="showFilesModalLabel">الملفات</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="showFiles">
          <div class="list-group">

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUser" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
  aria-labelledby="editUserLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="editUserLabel">تعديل المستخدم</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form data-profile-update="{{route('admin.user.update')}}" id="profileUpdate" method="post">
          @csrf
          <input type="hidden" name="id" class="getUserId">
          <div class="form-group col-md-12">
            <label class="form-label" for="fname">الاسم الكامل:</label>
            <input type="text" class="form-control" id="fname" name="name" required>
          </div>
          <div class="form-group col-md-12">
            <label class="form-label" for="mobno">رقم الهاتف:</label>
            <input type="text" class="form-control" id="mobno" name="mobile_number" required>
          </div>
          <div class="row">
            <div class="form-group col-md-12">
              <label class="form-label" for="uname">اسم المتسخدم:</label>
              <input type="text" class="form-control" id="uname" name="username" required>
            </div>
            <div class="form-group col-md-12">
              <label class="form-label" for="urule_name">الصلاحية:</label>
              <select class="form-control" name="rule_name" id="urule_name">
                <option value="admin">مسؤول النظام</option>
                <option value="user1">الحسابات</option>
                <option value="user2">المبيعات</option>
                <option value="user3">القانونية</option>
                <option value="user4">المراقب</option>
                <option value="user5">حسابات ومبيعات</option>
              </select>
            </div>
            <div class="appendRules">

            </div>
            <div class="form-group col-md-12">
              <label class="form-label" for="pass">كلمة السر:</label>
              <input type="password" placeholder="اترك الحقل فارغ اذا لم ترغب باعادة تعيين كلمة السر"
                class="form-control" id="pass" name="password" autocomplete="new-password">
              <input type="hidden" name="old_password" id="old_password">
            </div>
            <div class="form-group col-md-12">
              <label class="form-label" for="rpass">اعادة كتابة كلمة السر:</label>
              <input type="password" class="form-control" id="rpass" name="password_confirmation"
                autocomplete="new-password">
            </div>
          </div>
          <button type="submit" class="btn btn-primary updatebtn">حفظ المعلومات</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div class="modal fade" id="logsModal" tabindex="-1" aria-labelledby="logsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="logsModalLabel"></h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <ul class="list-group list-group-flush appendLogs">

        </ul>
      </div>
    </div>
  </div>
</div>