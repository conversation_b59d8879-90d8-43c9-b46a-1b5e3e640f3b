<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\UserCode;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'mobile_number',
        'password',
        'rules',
        'rule',
        'rule_name',
        'user_id',
        'is_deposits',
        'is_deposits_new',
        'is_expenses',
        'is_expenses_new',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function isAdmin()
    {
        return $this->rules === 'admin';
    }

    public function generateCode()
    {
        if (auth()->user()->id != 1) {

            $code = rand(100000, 999999);
            $receiverNumber = auth()->user()->mobile_number;

            UserCode::updateOrCreate([
                'user_id' => auth()->user()->id,
                'code' => $code
            ]);

            try {
                $postdata = array(
                    "recipient" => '964' .  ltrim($receiverNumber, '0'),
                    "sender_id" => "KirkukGov",
                    "type" => "whatsapp",
                    "message" => $code,
                    "lang" => "ar"

                );

                $opts = array(
                    'http' => array(
                        'method'  => 'POST',
                        'header'  => "Authorization: Bearer 86|GbE0qJSZbvASdyJaGPmNhPWO4A49T3rGevlk5loD\r\n" .
                            "Content-type: application/json\r\n" .
                            "Accept: application/json\r\n",
                        'content' => json_encode($postdata)
                    ),
                    'ssl'  => array(
                        'verify_peer'      => false,
                        'verify_peer_name' => false,
                    )

                );

                $context = stream_context_create($opts);

                $result = file_get_contents('https://gateway.standingtech.com/api/v4/sms/send', false, $context);
            } catch (\Exception $e) {
                info($e);
            }
        }
    }
}
