<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProjectController extends Controller
{
    public function index()
    {
        if (\Auth::user()->rule == 'admin' ||  \Auth::user()->rule == 'user6' ||  \Auth::user()->rule == 'user4') {
            return view('admin.project.index');
        } else {
            abort(404);
        }
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (\Auth::user()->rule == 'admin' ||  \Auth::user()->rule == 'user6') {
            return view('admin.project.newdeposit');
        } else {
            abort(404);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $id = \Auth::user()->id;
        if ($request->ajax()) {
            $validated = $request->validate(
                [
                    'day'                   => 'required',
                    'month'                 => 'required',
                    'year'                  => 'required',
                    'deposit_name'          => 'required',
                    'deposit_amount'        => 'required',
                    'deposit_details'       => 'required',
                ],
                [
                    'day.required'                              => 'يرجى اختيار اليوم',
                    'month.required'                            => 'يرجى اختيار الشهر',
                    'year.required'                             => 'يرجى اختيار السنة',
                    'deposit_name.required'                     => 'يرجى ادخال المودع',
                    'deposit_amount.required'                   => 'يرجى ادخال مبلغ الايداع',
                    'deposit_details.required'                  => 'يرجى كتابة التفاصيل',
                ]
            );


            if ($request->hasFile('deposit_file')) {
                $allfiles = array();
                $files = $request->file('deposit_file');

                foreach ($files as $key => $file) {
                    if ($file->isValid()) {
                        $filePath = $file->store('uploads/deposit', 'public');
                        $fileName = $filePath;
                        $allfiles[] = $fileName;
                    }
                }
                $empty_array = array($allfiles);
                if (!empty($empty_array)) {
                    $res_file = implode(',', $allfiles);
                } else {
                    $res_file = NULL;
                }
            } else {
                $res_file = NULL;
            }
            $depositid = DB::table('project_deposits_expenses')->insertGetId([
                'day'                       => $request->day,
                'month'                     => $request->month,
                'year'                      => $request->year,
                'date'                      => $request->year . '-' . $request->month . '-' . $request->day,
                'name'                      => $request->deposit_name,
                'amount'                    => $request->deposit_amount,
                'details'                   => $request->deposit_details,
                'file'                      => $res_file,
                'type'                      => 1,
                'user_id'                   => $id,
                'created_at'                => date('Y-m-d H:i:s'),
                'updated_at'                => date('Y-m-d H:i:s'),
            ]);

            DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $depositid, 'اضافة مشروع', 'تم اضافة سند الايداع رقم الاي دي ' . $depositid . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);

            return response()->json(['state' => 'success']);
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */

    public function show(Request $request)
    {
        if (\Auth::user()->rule == 'admin' ||  \Auth::user()->rule == 'user6' ||  \Auth::user()->rule == 'user4') {

            if ($request->ajax()) {
                $output = '';
                $pagination = 10;

                $data = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', '1')
                    ->where(function ($q) use ($request) {
                        if ($request->get('query') != '') {
                            return $q->where('name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query2') != '') {
                            return $q->where('details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query3') != '' && $request->get('query4') == '') {
                            return $q->where('date', '=',  $request->get('query3'));
                        }
                    })
                    ->where(function ($q) use ($request) {
                        if ($request->get('query3') != '' && $request->get('query4') != '') {
                            return $q->where('date', '>=', $request->get('query3'))
                                ->where('date', '<=', $request->get('query4'));
                        }
                    })
                    ->orderBy('date', 'desc');
                $data3 = $data->get();
                $data2 = $data->paginate($pagination);
                $total_row = $data2->count();
                $sum = 0;
                if ($total_row > 0) {
                    $output .= '<div class="table-responsive"><table class="table table-striped table-hover table-bordered">
                             <thead class="table-dark">
                                 <tr>
                                     <th scope="col">#</th>
                                     <th scope="col">التاريخ</th>
                                     <th scope="col">المبلغ</th>
                                     <th scope="col">المودع</th>
                                     <th scope="col">التفاصيل</th>
                                     <th scope="col">الادارة</th>
                                 </tr>
                             </thead><tbody>';
                    foreach ($data3 as $key => $value) {
                        $sum += str_replace(',', '', $value->amount);
                    }
                    foreach ($data2 as $row) {
                        $output .= '<tr>
                                     <td>' . $row->id . '</td>
                                     <td>' . $row->date . '</td>
                                     <td>' . $row->amount . '</td>
                                     <td>' . $row->name . '</td>
                                     <td>' . $row->details . '</td>
                                     <td>
                                         <button type="button" class="me-2 btn ' . (!empty($row->file) ? 'btn-info showfiles' : 'btn-light') . ' btn-sm" id="' . $row->id . '" data-url="' . url('') . '" data-get-files="' . $row->file . '"
                                          title="الملفات"><i class="fa-solid fa-file"></i></button>';
                        if (\Auth::user()->rule != 'user4') {
                            $output .= '<button type="button" class="btn btn-primary edit btn-sm" data-type-form="1" id="' . $row->id . '"
                                        data-day="' . $row->day . '" 
                                        data-month="' . $row->month . '" 
                                        data-year="' . $row->year . '" 
                                        data-deposit-name="' . $row->name . '"
                                        data-deposit-amount="' . $row->amount . '"
                                        data-deposit-details="' . $row->details . '"
                                        data-deposit-file="' . $row->file . '"
                        ><i
                                                 class="fa-solid fa-pen-to-square" title="تعديل"></i></button>
 
                                         <button type="button" class="btn btn-danger btn-sm delete" data-url-delete="' . route('project.deposits.destroy') . '" 
                                         id="' . $row->id . '" title="حذف"><i
                                                 class="fa-solid fa-trash"></i></button>';
                        }
                        $output .= '<button type="button" class="btn btn-warning btn-sm showlogs ms-2" data-logs-type="2" data-url-logs="' . route('logs') . '" id="' . $row->id . '" title="سجلات"><i class="fa-solid fa-circle-question"></i></button>';

                        $output .= '</td>
                                 </tr>';
                    }
                    $output .= '</tbody></table></div>';
                    $output .= $data2->links();
                } else {
                    $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
                }
                return response()->json([
                    'table_data' => $output,
                    'sum' => number_format($sum, 0)
                ]);
            }
            return view('admin.project.showdeposit');
        } else {
            abort(404);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if (\Auth::user()->rule != 'user4') {
            if ($request->ajax()) {
                $validated = $request->validate(
                    [
                        'day'                   => 'required',
                        'month'                 => 'required',
                        'year'                  => 'required',
                        'deposit_name'          => 'required',
                        'deposit_amount'        => 'required',
                        'deposit_details'       => 'required',
                    ],
                    [
                        'day.required'                              => 'يرجى اختيار اليوم',
                        'month.required'                            => 'يرجى اختيار الشهر',
                        'year.required'                             => 'يرجى اختيار السنة',
                        'deposit_name.required'                     => 'يرجى ادخال المودع',
                        'deposit_amount.required'                   => 'يرجى ادخال مبلغ الايداع',
                        'deposit_details.required'                  => 'يرجى كتابة التفاصيل',
                    ]
                );

                if ($request->hasFile('deposit_file')) {
                    $allfiles = array();
                    $files = $request->file('deposit_file');

                    foreach ($files as $key => $file) {
                        if ($file->isValid()) {
                            $filePath = $file->store('uploads/deposit', 'public');
                            $fileName = $filePath;
                            $allfiles[] = $fileName;
                        }
                    }
                    $empty_array = array($allfiles);
                    if (!empty($empty_array)) {
                        $res_file = implode(',', $allfiles);
                    } else {
                        $res_file = $request->deposit_file_old;
                    }
                } else {
                    $res_file = $request->deposit_file_old;
                }

                DB::table('project_deposits_expenses')
                    ->where('id', $request->id)
                    ->update(
                        [
                            'day' => $request->day,
                            'month' => $request->month,
                            'year' => $request->year,
                            'date' => $request->year . '-' . $request->month . '-' . $request->day,
                            'name' => $request->deposit_name,
                            'amount' => $request->deposit_amount,
                            'details' => $request->deposit_details,
                            'file' => $res_file,
                        ]
                    );
                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'تحديث مشروع', 'تم تحديث سند الايداع رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return response()->json(['state' => 'success']);
            }
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        if (\Auth::user()->rule != 'user4') {
            if ($request->ajax()) {
                DB::table('project_deposits_expenses')
                    ->where('id', $request->id)
                    ->update(['isDelete' => 1]);
                DB::insert('insert into logs (user_id, deposit_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?, ?)', [\Auth::user()->id, $request->id, 'حذف مشروع', 'تم حذف سند الايداع رقم الاي دي ' . $request->id . ' بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
                return 'success';
            }
        }
    }
    public function printreportdaily(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', 1)->whereDate('created_at', '=', Carbon::today()->toDateString())->orderBy('id', 'desc')->latest()->get();
            $total_row = $deposit->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<p>التاريخ: ' . Carbon::today()->toDateString() . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">رقم السند</th>
                                <th scope="col">التاريخ</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">المودع</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->date . '</td>
                                <td>' . $row->amount . '</td>
                                <td>' . $row->name . '</td>
                                <td>' . $row->details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->amount != null ? $row->amount : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="3">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة', 'تم طباعة سند الايداع المشروع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
    public function printreport(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $deposit = DB::table('project_deposits_expenses')->where('isDelete', 0)->where('type', 1)
                ->where(function ($q) use ($request) {
                    if ($request->get('query') != '') {
                        return $q->where('name', 'LIKE', '%' . str_replace(' ', '%', $request->get('query')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query2') != '') {
                        return $q->where('details', 'LIKE', '%' . str_replace(' ', '%', $request->get('query2')) . '%');
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') == '') {
                        return $q->where('date', '=',  $request->get('query3'));
                    }
                })
                ->where(function ($q) use ($request) {
                    if ($request->get('query3') != '' && $request->get('query4') != '') {
                        return $q->where('date', '>=', $request->get('query3'))
                            ->where('date', '<=', $request->get('query4'));
                    }
                })
                ->orderBy('id', 'desc')
                ->get();
            $total_row = $deposit->count();
            $sum = 0;
            if ($total_row > 0) {
                $output .= '<p>تم سحب هذا التقرير بتاريخ: ' . Carbon::today()->toDateString() . ' الساعة ' . date('h:i a') . '</p><table class="table table-bordered text-center">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">رقم السند</th>
                                <th scope="col">التاريخ</th>
                                <th scope="col">المبلغ</th>
                                <th scope="col">المودع</th>
                                <th scope="col">التفاصيل</th>
                            </tr>
                        </thead><tbody>';
                foreach ($deposit as $key => $row) {

                    $output .= '<tr>
                                <td>' . ($key + 1) . '</td>
                                <td>' . $row->date . '</td>
                                <td>' . $row->amount . '</td>
                                <td>' . $row->name . '</td>
                                <td>' . $row->details . '</td>
                            </tr>';
                    $sum += str_replace(',', '', $row->amount != null ? $row->amount : 0);
                }
                $output .= '<tr><td colspan="2">المجموع</td><td colspan="4">' . number_format($sum, 0) . ' دينار عراقي</td></tr></tbody></table>';
                DB::insert('insert into logs (user_id, type, action, created_at, updated_at) values (?, ?, ?, ?, ?)', [\Auth::user()->id, 'طباعة حسب الفرز', 'تم طباعة سند الايداع المشروع بواسطة ' . \Auth::user()->name, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            } else {
                $output .= 'empty';
            }
            return $output;
        }
    }
}
