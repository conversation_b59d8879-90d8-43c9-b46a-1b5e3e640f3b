@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);
:root {
    --bs-primary: #091e3e !important;
}
::-webkit-scrollbar-thumb {
    background: #091e3e;
}
body {
    font-family: "Noto <PERSON> Arabic", sans-serif !important;
}
.footer .footer-body {
    justify-content: flex-end !important;
}
.swiper-slide {
    width: 180px !important;
}
.swiper-wrapper {
    justify-content: center;
}
.iq-banner:not(.hide) + .content-inner {
    margin-top: -7rem !important;
}
.progress-widget {
    justify-content: center !important;
    margin-bottom: -17px !important;
    align-items: baseline;
}
.progress-widget .counter {
    display: none;
}
.progress-widget .progress-detail p {
    font-size: 14px;
    font-weight: bold;
    color: #000;
}
.alert-message {
    text-align: right !important;
}
.loader.simple-loader .loader-body {
    background: url(../images/loader.webp) no-repeat scroll center center !important;
}
.form-control {
    direction: rtl !important;
}
.iq-header-img {
    background-color: #1c7b7c !important;
}
.progress-widget .progress-detail {
    margin-right: 4px !important;
}
.newDeposit .input-group select,
.newDeposit .input-group label,
.newExpense .input-group select,
.newExpense .input-group label,
.offcanvas .input-group label,
.offcanvas .input-group select {
    background-color: #1c7b7c;
    color: #fff;
    border: 1px solid #1c7b7c;
}
.newDeposit .form-select,
.newDeposit .select2-selection {
    border: 1px solid #1c7b7c;
}
.btn-custom {
    --bs-btn-color: #ffffff;
    --bs-btn-bg: #1c7b7c;
    --bs-btn-border-color: #1f9d9f;
    --bs-btn-hover-color: #ffffff;
    --bs-btn-hover-bg: #1c7b7c;
    --bs-btn-hover-border-color: #1c7b7c;
    --bs-btn-focus-shadow-rgb: 60, 174, 109;
    --bs-btn-active-color: #ffffff;
    --bs-btn-active-bg: #1c7b7c;
    --bs-btn-active-border-color: #1c7b7c;
    --bs-btn-active-shadow: 0 0px 0px rgba(0, 0, 0, 0);
    --bs-btn-disabled-color: #ffffff;
    --bs-btn-disabled-bg: #1c7b7c;
    --bs-btn-disabled-border-color: #1c7b7c;
}
.credit-card-widget .card-header::after {
    background: none;
    bottom: unset;
    right: unset;
    -webkit-box-shadow: unset;
    box-shadow: unset;
}
.credit-card-widget .card-header::before {
    background: none;
    top: -45px;
    left: unset;
    -webkit-box-shadow: unset;
    box-shadow: unset;
}
.credit-card-widget .card-header {
    background: -webkit-linear-gradient(225deg, #091e3e 6%, #091e3e 56%);
    background: -o-linear-gradient(225deg, var(--bs-primary) 6%, #091e3e 56%);
    background: linear-gradient(225deg, #091e3e 6%, #091e3e 56%);
}
.hiddenNumber {
    display: none;
}
.credit-card-widget-hidden-number:hover .hiddenNumber {
    display: block;
}
.table > :not(caption) > * > * {
    padding: 4px;
}
.table th,
.table td {
    font-size: 13px;
    font-weight: bold;
}
.btn-sm,
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
.jq-toast-wrap.top-right {
    right: 0 !important;
}
.jq-has-icon {
    padding: 12px 22px 10px 50px !important;
}
.newClient span,
.newClient select,
.newClient input {
    font-size: 12px;
}
.newClient .input-group-text {
    padding: 3px;
}
.newClient .input-group-text,
.newClient input,
.newClient select {
    background-color: #1c7b7c;
    color: #fff;
    border: 1px solid #1c7b7c;
}
.newClient input,
.newClient select {
    font-weight: bold;
}
.error {
    color: #f00;
    font-size: 16px;
}
.select2 {
    width: 100% !important;
}
.select2-container[dir="rtl"]
    .select2-selection--single
    .select2-selection__rendered {
    padding-right: 25px !important;
}
.select2-results__option {
    font-size: 13px;
    color: #fff;
}
.select2-dropdown {
    background-color: #1c7b7c;
}
.form-control:disabled {
    color: #000;
}
.btn-warning {
    --bs-btn-color: #000;
    --bs-btn-bg: #ffc107;
    --bs-btn-border-color: #ffc107;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #ffca2c;
    --bs-btn-hover-border-color: #ffc720;
    --bs-btn-focus-shadow-rgb: 217, 164, 6;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #ffcd39;
    --bs-btn-active-border-color: #ffc720;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #ffc107;
    --bs-btn-disabled-border-color: #ffc107;
}
@media (max-width: 1200px) {
    .navbar h5 {
        display: none;
    }
}
@media (max-width: 600px) {
    .navbar h5 {
        display: none;
    }
}
@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}
