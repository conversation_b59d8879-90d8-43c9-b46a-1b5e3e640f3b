@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

/* ===== نظام الألوان الفلات الحديث ===== */
:root {
    /* الألوان الأساسية */
    --bs-primary: #2563eb !important;
    --bs-primary-dark: #1d4ed8 !important;
    --bs-primary-light: #3b82f6 !important;
    --bs-primary-lighter: #dbeafe !important;

    /* الألوان الثانوية */
    --bs-secondary: #64748b !important;
    --bs-secondary-dark: #475569 !important;
    --bs-secondary-light: #94a3b8 !important;

    /* ألوان الحالة */
    --bs-success: #10b981 !important;
    --bs-success-light: #d1fae5 !important;
    --bs-warning: #f59e0b !important;
    --bs-warning-light: #fef3c7 !important;
    --bs-danger: #ef4444 !important;
    --bs-danger-light: #fee2e2 !important;
    --bs-info: #06b6d4 !important;
    --bs-info-light: #cffafe !important;

    /* ألوان الخلفية */
    --bs-body-bg: #f8fafc !important;
    --bs-card-bg: #ffffff !important;
    --bs-sidebar-bg: #ffffff !important;
    --bs-navbar-bg: #ffffff !important;

    /* ألوان النص */
    --bs-text-primary: #1e293b !important;
    --bs-text-secondary: #64748b !important;
    --bs-text-muted: #94a3b8 !important;

    /* الحدود والظلال */
    --bs-border-color: #e2e8f0 !important;
    --bs-border-radius: 12px !important;
    --bs-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    --bs-box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* ===== التصميم العام ===== */
::-webkit-scrollbar-thumb {
    background: var(--bs-primary);
    border-radius: 6px;
}

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bs-border-color);
}

body {
    font-family: "Noto Kufi Arabic", sans-serif !important;
    background-color: var(--bs-body-bg) !important;
    color: var(--bs-text-primary) !important;
    line-height: 1.6;
}

/* ===== التخطيط العام ===== */
.footer .footer-body {
    justify-content: flex-end !important;
}

.swiper-slide {
    width: 180px !important;
    border-radius: var(--bs-border-radius) !important;
    transition: all 0.3s ease !important;
}

.swiper-slide:hover {
    transform: translateY(-4px);
    box-shadow: var(--bs-box-shadow-lg) !important;
}

/* ===== تحسينات البطاقات المحدثة ===== */
.swiper-slide {
    width: 280px !important;
    height: 220px !important;
    margin: 0 15px !important;
}

.swiper-slide .card-body {
    padding: 25px !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.swiper-slide .progress-widget {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
}

.swiper-slide .icon-wrapper {
    margin-bottom: 20px !important;
}

.swiper-slide .icon-circle {
    width: 70px !important;
    height: 70px !important;
    margin: 0 auto 15px auto !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50% !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.swiper-slide .icon-circle i {
    font-size: 28px !important;
    transition: all 0.3s ease !important;
}

.swiper-slide:hover .icon-circle {
    transform: scale(1.1) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.swiper-slide:hover .icon-circle i {
    transform: scale(1.1) !important;
}

.swiper-slide .progress-detail {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 20px !important;
}

.swiper-slide .progress-detail h6 {
    font-size: 18px !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
}

.swiper-slide .progress-detail small {
    font-size: 13px !important;
    opacity: 0.8 !important;
    line-height: 1.4 !important;
}

.swiper-slide .d-flex.gap-2 {
    margin-top: auto;
    gap: 10px !important;
}

.swiper-slide .btn-sm {
    font-size: 12px !important;
    padding: 10px 16px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    letter-spacing: 0.3px !important;
    border-width: 2px !important;
    transition: all 0.3s ease !important;
    min-height: 38px !important;
}

.swiper-slide .btn-sm i {
    font-size: 11px !important;
    margin-left: 5px !important;
}

.swiper-slide .btn-sm:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.swiper-wrapper {
    justify-content: center;
}

.iq-banner:not(.hide) + .content-inner {
    margin-top: -7rem !important;
}

/* ===== البطاقات والعناصر ===== */
.progress-widget {
    justify-content: center !important;
    margin-bottom: -17px !important;
    align-items: baseline;
}

.progress-widget .counter {
    display: none;
}

.progress-widget .progress-detail p {
    font-size: 14px;
    font-weight: 600;
    color: var(--bs-text-primary);
    margin: 0;
}

.alert-message {
    text-align: right !important;
}

.loader.simple-loader .loader-body {
    background: url(../images/loader.webp) no-repeat scroll center center !important;
}

.form-control {
    direction: rtl !important;
    border: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

.iq-header-img {
    background: linear-gradient(
        135deg,
        var(--bs-primary) 0%,
        var(--bs-primary-dark) 100%
    ) !important;
    border-radius: 0 0 24px 24px !important;
}

.progress-widget .progress-detail {
    margin-right: 4px !important;
}
/* ===== تصميم النماذج والمدخلات ===== */
.newDeposit .input-group select,
.newDeposit .input-group label,
.newExpense .input-group select,
.newExpense .input-group label,
.offcanvas .input-group label,
.offcanvas .input-group select {
    background-color: var(--bs-primary);
    color: #ffffff;
    border: 1px solid var(--bs-primary);
    border-radius: var(--bs-border-radius);
    transition: all 0.3s ease;
}

.newDeposit .input-group select:hover,
.newDeposit .input-group label:hover,
.newExpense .input-group select:hover,
.newExpense .input-group label:hover,
.offcanvas .input-group label:hover,
.offcanvas .input-group select:hover {
    background-color: var(--bs-primary-dark);
    border-color: var(--bs-primary-dark);
}

.newDeposit .form-select,
.newDeposit .select2-selection {
    border: 1px solid var(--bs-primary);
    border-radius: var(--bs-border-radius);
}

/* ===== تصميم الأزرار الفلات ===== */
.btn-custom {
    --bs-btn-color: #ffffff;
    --bs-btn-bg: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-hover-color: #ffffff;
    --bs-btn-hover-bg: var(--bs-primary-dark);
    --bs-btn-hover-border-color: var(--bs-primary-dark);
    --bs-btn-focus-shadow-rgb: 37, 99, 235;
    --bs-btn-active-color: #ffffff;
    --bs-btn-active-bg: var(--bs-primary-dark);
    --bs-btn-active-border-color: var(--bs-primary-dark);
    --bs-btn-active-shadow: 0 0px 0px rgba(0, 0, 0, 0);
    --bs-btn-disabled-color: #ffffff;
    --bs-btn-disabled-bg: var(--bs-secondary-light);
    --bs-btn-disabled-border-color: var(--bs-secondary-light);
    border-radius: var(--bs-border-radius);
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
    box-shadow: var(--bs-box-shadow);
}

.btn-custom:hover {
    transform: translateY(-1px);
    box-shadow: var(--bs-box-shadow-lg);
}

.btn-primary {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease !important;
    box-shadow: var(--bs-box-shadow) !important;
}

.btn-primary:hover {
    background-color: var(--bs-primary-dark) !important;
    border-color: var(--bs-primary-dark) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--bs-box-shadow-lg) !important;
}

.btn-success {
    background-color: var(--bs-success) !important;
    border-color: var(--bs-success) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
}

.btn-warning {
    background-color: var(--bs-warning) !important;
    border-color: var(--bs-warning) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
    color: #ffffff !important;
}

.btn-danger {
    background-color: var(--bs-danger) !important;
    border-color: var(--bs-danger) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
}

.btn-info {
    background-color: var(--bs-info) !important;
    border-color: var(--bs-info) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
}
/* ===== تصميم البطاقات الفلات ===== */
.card {
    border: none !important;
    border-radius: var(--bs-border-radius) !important;
    box-shadow: var(--bs-box-shadow) !important;
    background-color: var(--bs-card-bg) !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    box-shadow: var(--bs-box-shadow-lg) !important;
    transform: translateY(-2px) !important;
}

.card-header {
    background-color: transparent !important;
    border-bottom: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) var(--bs-border-radius) 0 0 !important;
    padding: 20px !important;
}

.card-body {
    padding: 20px !important;
}

.card-slide {
    background: linear-gradient(
        135deg,
        var(--bs-card-bg) 0%,
        #f1f5f9 100%
    ) !important;
    border: 1px solid var(--bs-border-color) !important;
}

.card-slide:hover {
    background: linear-gradient(
        135deg,
        var(--bs-primary-lighter) 0%,
        var(--bs-primary-light) 100%
    ) !important;
    border-color: var(--bs-primary) !important;
}

.credit-card-widget .card-header::after {
    background: none;
    bottom: unset;
    right: unset;
    -webkit-box-shadow: unset;
    box-shadow: unset;
}

.credit-card-widget .card-header::before {
    background: none;
    top: -45px;
    left: unset;
    -webkit-box-shadow: unset;
    box-shadow: unset;
}

.credit-card-widget .card-header {
    background: linear-gradient(
        135deg,
        var(--bs-primary) 0%,
        var(--bs-primary-dark) 100%
    ) !important;
    border-radius: var(--bs-border-radius) var(--bs-border-radius) 0 0 !important;
}
/* ===== عناصر إضافية ===== */
.hiddenNumber {
    display: none;
}

.credit-card-widget-hidden-number:hover .hiddenNumber {
    display: block;
}

/* ===== تصميم الجداول الفلات ===== */
.table {
    border-radius: var(--bs-border-radius) !important;
    overflow: hidden !important;
    box-shadow: var(--bs-box-shadow) !important;
    background-color: var(--bs-card-bg) !important;
}

.table > :not(caption) > * > * {
    padding: 12px 8px !important;
    border-color: var(--bs-border-color) !important;
}

.table th {
    background-color: var(--bs-primary) !important;
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    border: none !important;
}

.table td {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: var(--bs-text-primary) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: #f8fafc !important;
}

.table-hover > tbody > tr:hover > td {
    background-color: var(--bs-primary-lighter) !important;
    cursor: pointer;
}

/* ===== تصميم الأزرار الصغيرة ===== */
.btn-sm,
.btn-group-sm > .btn {
    padding: 8px 16px !important;
    font-size: 12px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

/* ===== الإشعارات ===== */
.jq-toast-wrap.top-right {
    right: 0 !important;
}

.jq-has-icon {
    padding: 12px 22px 10px 50px !important;
    border-radius: var(--bs-border-radius) !important;
}

/* ===== نماذج العملاء الجدد ===== */
.newClient span,
.newClient select,
.newClient input {
    font-size: 13px !important;
}

.newClient .input-group-text {
    padding: 8px 12px !important;
    background-color: var(--bs-primary) !important;
    color: #ffffff !important;
    border: 1px solid var(--bs-primary) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 600 !important;
}

.newClient input,
.newClient select {
    background-color: var(--bs-card-bg) !important;
    color: var(--bs-text-primary) !important;
    border: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.newClient input:focus,
.newClient select:focus {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* ===== رسائل الخطأ ===== */
.error {
    color: var(--bs-danger) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

/* ===== Select2 التصميم الفلات ===== */
.select2 {
    width: 100% !important;
}

.select2-container[dir="rtl"]
    .select2-selection--single
    .select2-selection__rendered {
    padding-right: 25px !important;
}

.select2-selection {
    border: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) !important;
    background-color: var(--bs-card-bg) !important;
}

.select2-results__option {
    font-size: 13px !important;
    color: var(--bs-text-primary) !important;
    padding: 10px 15px !important;
}

.select2-dropdown {
    background-color: var(--bs-card-bg) !important;
    border: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) !important;
    box-shadow: var(--bs-box-shadow-lg) !important;
}

.select2-results__option--highlighted {
    background-color: var(--bs-primary) !important;
    color: #ffffff !important;
}

.form-control:disabled {
    background-color: var(--bs-secondary-light) !important;
    color: var(--bs-text-secondary) !important;
    border-color: var(--bs-border-color) !important;
}
/* ===== تصميم الشريط الجانبي الفلات ===== */
.sidebar {
    background-color: var(--bs-sidebar-bg) !important;
    border-right: 1px solid var(--bs-border-color) !important;
    box-shadow: var(--bs-box-shadow-lg) !important;
    transition: all 0.3s ease !important;
}

.sidebar-header {
    padding: 20px !important;
    border-bottom: 1px solid var(--bs-border-color) !important;
}

.sidebar .nav-link {
    color: var(--bs-text-primary) !important;
    padding: 12px 20px !important;
    margin: 4px 12px !important;
    border-radius: var(--bs-border-radius) !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
}

.sidebar .nav-link:hover {
    background-color: var(--bs-primary-lighter) !important;
    color: var(--bs-primary) !important;
    transform: translateX(-4px) !important;
}

.sidebar .nav-link.active {
    background-color: var(--bs-primary) !important;
    color: #ffffff !important;
    box-shadow: var(--bs-box-shadow) !important;
}

.sidebar .nav-link .icon {
    margin-left: 12px !important;
    width: 20px !important;
    height: 20px !important;
}

/* ===== تصميم شريط التنقل العلوي الفلات ===== */
.navbar {
    background-color: var(--bs-navbar-bg) !important;
    border-bottom: 1px solid var(--bs-border-color) !important;
    box-shadow: var(--bs-box-shadow) !important;
    padding: 16px 0 !important;
}

.navbar .navbar-brand {
    font-weight: 700 !important;
    color: var(--bs-primary) !important;
}

.navbar .nav-link {
    color: var(--bs-text-primary) !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border-radius: var(--bs-border-radius) !important;
    transition: all 0.3s ease !important;
}

.navbar .nav-link:hover {
    background-color: var(--bs-primary-lighter) !important;
    color: var(--bs-primary) !important;
}

.dropdown-menu {
    border: 1px solid var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius) !important;
    box-shadow: var(--bs-box-shadow-lg) !important;
    background-color: var(--bs-card-bg) !important;
}

.dropdown-item {
    color: var(--bs-text-primary) !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background-color: var(--bs-primary-lighter) !important;
    color: var(--bs-primary) !important;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 1200px) {
    .navbar h5 {
        display: none;
    }

    .sidebar {
        transform: translateX(100%) !important;
    }

    .sidebar.show {
        transform: translateX(0) !important;
    }
}

@media (max-width: 600px) {
    .navbar h5 {
        display: none;
    }

    .card {
        margin: 8px !important;
    }

    .swiper-slide {
        width: 150px !important;
    }

    .table-responsive {
        font-size: 12px !important;
    }
}

/* ===== الرسوم المتحركة ===== */
@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== تحسينات إضافية للتصميم الفلات ===== */
.badge {
    border-radius: 6px !important;
    font-weight: 600 !important;
    padding: 6px 12px !important;
}

.alert {
    border: none !important;
    border-radius: var(--bs-border-radius) !important;
    box-shadow: var(--bs-box-shadow) !important;
}

.modal-content {
    border: none !important;
    border-radius: var(--bs-border-radius) !important;
    box-shadow: var(--bs-box-shadow-lg) !important;
}

.modal-header {
    border-bottom: 1px solid var(--bs-border-color) !important;
    background-color: var(--bs-primary-lighter) !important;
}

.modal-footer {
    border-top: 1px solid var(--bs-border-color) !important;
}

/* ===== تأثيرات التحويم والتفاعل ===== */
.btn,
.card,
.table,
.form-control,
.nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn:active {
    transform: scale(0.98) !important;
}

/* ===== تحسينات خاصة بالتصميم الجديد ===== */
.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.bg-success-light {
    background-color: var(--bs-success-light) !important;
}

.bg-primary-light {
    background-color: var(--bs-primary-lighter) !important;
}

.bg-danger-light {
    background-color: var(--bs-danger-light) !important;
}

.bg-warning-light {
    background-color: var(--bs-warning-light) !important;
}

.bg-info-light {
    background-color: var(--bs-info-light) !important;
}

.bg-secondary-light {
    background-color: #f1f5f9 !important;
}

.active-card {
    background: linear-gradient(
        135deg,
        var(--bs-primary) 0%,
        var(--bs-primary-dark) 100%
    ) !important;
    color: #ffffff !important;
    border-color: var(--bs-primary) !important;
}

.active-card .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.active-indicator {
    width: 4px;
    height: 20px;
    background-color: var(--bs-primary);
    border-radius: 2px;
}

.nav-link-header {
    padding: 8px 0;
    border-bottom: 1px solid var(--bs-border-color);
    margin-bottom: 8px;
}

.icon-wrapper {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sub-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-icon {
    position: relative;
    display: inline-block;
}

.user-profile {
    padding: 8px 16px;
    border-radius: var(--bs-border-radius);
    transition: all 0.3s ease;
}

.user-profile:hover {
    background-color: var(--bs-primary-lighter);
}

.avatar-wrapper {
    position: relative;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.brand-text {
    line-height: 1.2;
}

.transition-transform {
    transition: transform 0.3s ease;
}

.collapse.show + .nav-link .transition-transform {
    transform: rotate(180deg);
}

/* ===== تحسين الطباعة ===== */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .dropdown {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}
