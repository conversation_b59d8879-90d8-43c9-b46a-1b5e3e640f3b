<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('_installments', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id')->nullable();
            $table->integer('sold_type')->nullable();
            $table->integer('price_name')->nullable();
            $table->integer('price')->nullable();
            $table->integer('price_date')->nullable();
            $table->integer('status')->nullable()->default(0);;
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('_installments');
    }
};
