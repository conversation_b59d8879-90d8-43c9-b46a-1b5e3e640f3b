$(function () {
    "use strict";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    $(document).keypress(function (event) {
        if (event.which == "13") {
            event.preventDefault();
        }
    });
    load_amounts();
    function load_amounts() {
        $.ajax({
            url: $("#getAmountUrl").val(),
            method: "GET",
            dataType: "json",
            success: function (data) {
                $(".totalAmount").text(data.project3);
                $(".totalDeposits").text(data.project1);
                $(".totalExpenses").text(data.project2);
            },
            error: function (data) {
                console.log(data);
            },
        });
    }
    load_doors();
    function load_doors() {
        $.ajax({
            url: $("#getDoorsUrl").val(),
            method: "POST",
            dataType: "json",
            success: function (data) {
                $("#dynamic_data_doors_type2").html(data.table_data_type2);

                $("[name='expense_door']").html(
                    '<option value="">باب الصرف</option>' +
                        data.select_data_type2
                );
            },
            error: function (data) {
                console.log(data);
            },
        });
    }

    // Add Deposit
    $(".newDeposit").on("submit", function (e) {
        e.preventDefault();

        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $(".newDeposit").attr("data-form-deposit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $.toast({
                        text: "تم اضافة سند الايداع بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                    $(".js-value").text("");
                    $(".newDeposit").trigger("reset");
                }
            },
            complete: function () {
                $(".btn-send").html("اضافة");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (d) {
                console.log(d["responseJSON"]["message"]);
                $.alert({
                    title: "تنبيه!",
                    content: d["responseJSON"]["message"],
                    type: "red",
                    typeAnimated: true,
                    rtl: true,
                });
            },
        });
    });
    // Print ReportDaily
    $(document).on("click", ".edit", function () {
        if ($(this).attr("data-type-form") == 1) {
            $("#geteditId").val($(this).attr("id"));
            $("#input1").val($(this).attr("data-day"));
            $("#input2").val($(this).attr("data-month"));
            $("#input3").val($(this).attr("data-year"));

            $("#input5").val($(this).attr("data-deposit-name"));
            $("#input7").val($(this).attr("data-deposit-amount"));
            $("#input8").val($(this).attr("data-deposit-details"));
            $("#input10").val($(this).attr("data-deposit-file"));
            if ($(this).attr("data-deposit-file") != "") {
                var files = $(this).attr("data-deposit-file").split(",");
                $(".js-value").text(
                    files.length + " " + (files.length == 1 ? "ملف" : "ملفات")
                );
            } else {
                $(".js-value").text("");
            }

            $("#EditStaticBackdrop").offcanvas("show");
        } else {
            $("#geteditId2").val($(this).attr("id"));
            $(".input1").val($(this).attr("data-day"));
            $(".input2").val($(this).attr("data-month"));
            $(".input3").val($(this).attr("data-year"));
            $(".input4").val($(this).attr("data-expense-door"));
            $(".input5").val($(this).attr("data-expense-name"));
            $(".input7").val($(this).attr("data-expense-amount"));
            $(".input8").val($(this).attr("data-expense-details"));
            $(".input10").val($(this).attr("data-expense-file"));
            if ($(this).attr("data-expense-file") != "") {
                var files = $(this).attr("data-expense-file").split(",");
                $(".js-value").text(
                    files.length + " " + (files.length == 1 ? "ملف" : "ملفات")
                );
            } else {
                $(".js-value").text("");
            }

            $("#Edit2StaticBackdrop").offcanvas("show");
        }
    });
    $(document).on("click", ".js-value-del", function () {
        $("#input10").val("");
        $(".js-value").text("");
    });
    // Edit Deposit
    $("#ُEditFormDeposit").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $("#ُEditFormDeposit").attr("data-edit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $.toast({
                        text: "تم تحديث سند الايداع بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                    load_data(1);
                    $("#ُEditFormDeposit").trigger("reset");
                    $("#EditStaticBackdrop").offcanvas("hide");
                }
            },
            complete: function () {
                $(".btn-send").html("تحديث");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Edit Expense
    $("#ُEdit2FormDeposit").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $("#ُEdit2FormDeposit").attr("data-edit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $.toast({
                        text: "تم تحديث سند الصرف بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                    load_data(1);
                    $("#ُEdit2FormDeposit").trigger("reset");
                    $("#Edit2StaticBackdrop").offcanvas("hide");
                }
            },
            complete: function () {
                $(".btn-send").html("تحديث");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    /* *******************************************
     *********************************************
     *********************************************
     ************* Delete Record Ajax ************
     *********************************************
     *********************************************
     *********************************************/
    $(document).on("click", ".delete", function () {
        var deleteurl = $(this),
            id = deleteurl.attr("id");
        $.confirm({
            title: "تأكيد!",
            content: "هل انت متأكد؟",
            rtl: true,
            buttons: {
                نعم: function () {
                    $.ajax({
                        method: "POST",
                        url: deleteurl.attr("data-url-delete"),
                        data: {
                            id: id,
                        },
                        success: function (data) {
                            if (data == "success") {
                                load_data(1);
                            }
                        },
                        error: function (d) {
                            console.log(d);
                        },
                    });
                },
                كلا: function () {},
            },
        });
    });
    /* *******************************************
     *********************************************
     *********************************************
     **************** Add Expense ****************
     *********************************************
     *********************************************
     *********************************************/
    $(".newExpense").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $(".newExpense").attr("data-form-expense-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $(".newExpense").trigger("reset");
                    $.toast({
                        text: "تم اضافة سند الايداع بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                }
            },
            error: function (data) {
                console.log(data);
            },
            complete: function () {
                $(".btn-send").html("اضافة");
                $(".btn-send").removeAttr("disabled");
            },
        });
    });
    // Print ReportDaily
    $(".printReportDaily").on("click", function () {
        $.ajax({
            method: "POST",
            url: $(this).attr("data-url-print-daily"),
            success: function (data) {
                if (data == "empty") {
                    $.alert({
                        title: "تنبيه!",
                        content: "لا يوجد سندات مضافة اليوم لعرضها!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                } else {
                    let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=900,height=600,left=100,top=100`;
                    var newWin2 = window.open("", "Print-Window", params);

                    newWin2.document.open();

                    newWin2.document.write(
                        '<html dir="rtl"><head><meta charset="utf-8" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous"/>' +
                            '<link rel="stylesheet" href="https://www.obe.seio.uk/public/assets/css/style.css"/> ' +
                            '<style>h6 { font-size: 14px;} .table tbody tr td, .table thead tr th { font-size: 10px!important;}.table tbody tr td {background-color: #00000000;}</style></head><body onload="window.print();">' +
                            '<div style="">' +
                            '<div style="margin-top: 10px">' +
                            data +
                            "</div>" +
                            "</div>" +
                            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>' +
                            "</body>" +
                            "</html>"
                    );

                    newWin2.document.close();

                    // setTimeout(function () {
                    //     newWin2.close();
                    // }, 10);
                }
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Print Report
    $(".printReport").on("click", function () {
        $.ajax({
            method: "POST",
            url: $(this).attr("data-url-print"),
            method: "GET",
            data: {
                query: $("#search").val(),
                query2: $("#search2").val(),
                query3: $("#search3").val(),
                query4: $("#search4").val(),
                query5: $("#search5").val(),
            },
            success: function (data) {
                if (data == "empty") {
                    $.alert({
                        title: "تنبيه!",
                        content: "لا يوجد سندات مضافة اليوم لعرضها!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                } else {
                    let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=900,height=600,left=100,top=100`;
                    var newWin2 = window.open("", "Print-Window", params);

                    newWin2.document.open();

                    newWin2.document.write(
                        '<html dir="rtl"><head><meta charset="utf-8" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous"/>' +
                            '<link rel="stylesheet" href="https://www.obe.seio.uk/public/assets/css/style.css"/> ' +
                            '<style>h6 { font-size: 14px;} .table tbody tr td, .table thead tr th { font-size: 9px!important;}.table tbody tr td {background-color: #00000000;}</style></head><body onload="window.print();">' +
                            "<div>" +
                            '<div style="margin-top: 10px">' +
                            data +
                            "</div>" +
                            "</div>" +
                            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>' +
                            "</body>" +
                            "</html>"
                    );

                    newWin2.document.close();

                    // setTimeout(function () {
                    //     newWin2.close();
                    // }, 10);
                }
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Check Upload file
    $("#input9").on("change", function () {
        var fileInput = $(this);
        var files = fileInput[0].files;

        // Check if any files were selected
        if (files.length > 0) {
            var validExtensions = [
                "jpg",
                "jpeg",
                "png",
                "pdf",
                "doc",
                "docx",
                "xlsx",
            ];

            // Iterate over the selected files
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                var fileExtension = file.name.split(".").pop().toLowerCase();

                // Check if the file extension is valid
                if (validExtensions.indexOf(fileExtension) === -1) {
                    $.alert({
                        title: "تنبيه!",
                        content: "نوع ملف " + file.name + " غير معروف",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    fileInput.val(""); // Clear the file input
                    $(".js-value").text("");
                    return;
                }
                if (file.size > 10485760) {
                    $.alert({
                        title: "تنبيه!",
                        content: "الحجم المسموح به هو 10MB!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    fileInput.val(""); // Clear the file input
                    $(".js-value").text("");
                    return;
                }
                $(".js-value").text(i + 1 + " " + (i == 0 ? "ملف" : "ملفات"));
            }
        }
    });
    // Show Files
    $(document).on("click", ".showfiles", function () {
        $("#showFilesModal").modal("show");

        var arr = $(this).attr("data-get-files").split(","),
            listitem = [],
            url = $(this).attr("data-url");

        arr.forEach(function (ele, idx) {
            if (ele) {
                listitem.push(
                    '<a href="' +
                        url +
                        "/" +
                        ele +
                        '" target="_blank" class="list-group-item list-group-item-info">الملف ' +
                        (idx + 1) +
                        "</a>"
                );
            }
        });

        $(".showFiles .list-group").html("");
        $(".showFiles .list-group").html(listitem);
    });
    // Jquery Dependency
    $(document).on("keyup", "input[data-type='currency']", function () {
        formatCurrency($(this));
    });
    $(document).on("blur", "input[data-type='currency']", function () {
        formatCurrency($(this));
    });
    function formatNumber(n) {
        // format number 1000000 to 1,234,567
        return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function formatCurrency(input, type = 0) {
        // appends $ to value, validates decimal side
        // and puts cursor back in right position.

        // get input value
        if (type == 1) {
            var input_val = input;
        } else {
            var input_val = input.val();
        }

        // don't validate empty input
        if (input_val === "") {
            return;
        }

        // original length
        var original_len = input_val.length;

        // initial caret position
        var caret_pos = input.prop("selectionStart");

        // check for decimal
        if (input_val.indexOf(".") >= 0) {
            // get position of first decimal
            // this prevents multiple decimals from
            // being entered
            var decimal_pos = input_val.indexOf(".");

            // split number by decimal point
            var left_side = input_val.substring(0, decimal_pos);
            var right_side = input_val.substring(decimal_pos);

            // add commas to left side of number
            left_side = formatNumber(left_side);

            // validate right side
            right_side = formatNumber(right_side);

            // Limit decimal to only 2 digits
            right_side = right_side.substring(0, 2);

            // join number by .
            input_val = "" + left_side + "." + right_side;
        } else {
            // no decimal entered
            // add commas to number
            // remove all non-digits
            input_val = formatNumber(input_val);
            input_val = "" + input_val;
        }

        // send updated string to input
        input.val(input_val);

        // put caret back in the right position
        var updated_len = input_val.length;
        caret_pos = updated_len - original_len + caret_pos;
        input[0].setSelectionRange(caret_pos, caret_pos);
    }

    // Show logs
    $(document).on("click", ".showlogs", function () {
        var showlogs = $(this),
            id = showlogs.attr("id"),
            type = showlogs.attr("data-logs-type");
        console.log(type);

        $.ajax({
            method: "POST",
            url: showlogs.attr("data-url-logs"),
            data: {
                id: id,
                type: type,
            },
            success: function (data) {
                $(".appendLogs").html(data);
                $("#logsModal").modal("show");
            },
            error: function (d) {
                console.log(d);
            },
        });
    });
    $(document).on("change", "[name='currency']", function () {
        if ($(this).val() == "USD") {
            $(".hidecurrencyinput").removeAttr("style");
            $("#input777").attr("name", "currency_price");
            $("#input777").attr("required", "required");
            $("#input7777").attr("name", "price_dollar");
            $("#input7777").attr("required", "required");
        } else {
            $(".hidecurrencyinput").attr("style", "display:none");
            $("#input777").removeAttr("name");
            $("#input777").removeAttr("required");
            $("#input7777").removeAttr("name");
            $("#input7777").removeAttr("required");
        }
    });
    $(document).on("keyup", "#input777,#input7777", function () {
        var input777 = $("#input777").val(),
            input7777 = $("#input7777").val(),
            res =
                Number(input777.replace(/\,/g, "")) *
                Number(input7777.replace(/\,/g, ""));
        formatCurrency($('[name="expense_amount"]').val(Math.floor(res)));
    });
});
window.forceReload = function () {
    if (!window.fetch) return document.location.reload(true);
    var els = document.getElementsByTagName("*");
    for (var i = 0; i < els.length; i++) {
        var src = "";
        if (els[i].tagName == "A") continue;
        if (!src && els[i].src) src = els[i].getAttribute("src");
        if (!src && els[i].href) src = els[i].getAttribute("href");
        if (!src) continue;
        fetch(src, { cache: "reload" });
    }
    return document.location.reload(true);
};
