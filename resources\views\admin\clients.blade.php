@extends('admin.layouts.app')

@section('title', 'العملاء')
@section('content')
<div class="col-md-12 col-lg-12">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="card-body">
                    <form class="row mb-1" id="search_form2" data-url-show-client="{{route('clients')}}">

                        <div class="col-2">
                            <div style="text-align: center;">
                                <input class="form-control" type="text" id="search" placeholder="اسم العميل" />
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1">نوع البيع</span>
                                <select class="form-control" id="search2" aria-describedby="basic-addon1">
                                    <option value="">الكل</option>
                                    <option value="غير محدد">غير محدد</option>
                                    <option value="نقداً">نقداً</option>
                                    <option value="اقساط">اقساط</option>
                                    <option value="دفعات محددة">دفعات محددة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon2">من</span>
                                <input type="date" class="form-control" aria-describedby="basic-addon2" id="search3">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon3">الى</span>
                                <input type="date" class="form-control" aria-describedby="basic-addon3" id="search4">
                            </div>
                        </div>
                    </form>
                    <div class="mb-1">
                        <button type="button" class="btn btn-dark printReport"
                            data-url-print="{{route('clients.printreport')}}">
                            طباعة حسب الفرز
                        </button>
                    </div>
                    <div id="dynamic_table" class="text-center">

                    </div>
                    <div style="clear:both"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection