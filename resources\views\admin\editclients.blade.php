@extends('admin.layouts.app')

@section('title', 'تعديل العميل')
@section('content')

<div class="col-md-12 col-lg-12">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">

                <div class="card" data-aos="fade-up" data-aos-delay="800">
                    <div class="flex-wrap card-header d-flex justify-content-between align-items-center">

                    </div>
                    <div class="card-body">
                        <div id="d-main" class="d-main">
                            <div class="newClient">
                                <form method="POST" action="{{route('clients.update')}}" id="newClient"
                                    enctype="multipart/form-data">
                                    @csrf
                                    <input type="hidden" name="id" value="{{$client->id}}">
                                    <div class="row mb-3">
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input1">رقم الدار</span>
                                                <input type="text" class="form-control" name="house_no"
                                                    value="{{$client->house_no}}"
                                                    data-check-url="{{route('clients.check')}}"
                                                    aria-describedby="input1" required>
                                            </div>
                                        </div>
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input2">مساحة الدار</span>
                                                <input type="text" class="form-control" name="house_space"
                                                    value="{{$client->house_space}}" aria-describedby="input2" required>
                                            </div>
                                        </div>
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input3">مساحة البناء</span>
                                                <input type="text" class="form-control" name="buld_space"
                                                    value="{{$client->buld_space}}" aria-describedby="input3">
                                            </div>
                                        </div>
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input4">عدد الغرف</span>
                                                <select class="form-control" name="rooms_no" aria-describedby="input4">
                                                    <option value="">اختر...</option>
                                                    <?php
                                                    for ($i = 1; $i <= 10; $i++) { 
                                                        echo '<option value="' . $i .'" ' . ($i == $client->rooms_no ? 'selected' : '') . '>'.$i.'</option>';
                                                        }
                                                    ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input5">القيمة الكلية للدار</span>
                                                <input type="text" class="form-control" name="totol_house_price"
                                                    value="{{$client->totol_house_price}}" aria-describedby="input5"
                                                    data-type='currency'>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input6">اسم المشتري</span>
                                                <input type="text" class="form-control" name="name_purshes"
                                                    value="{{$client->name_purshes}}" aria-describedby="input6"
                                                    required>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input7">اسم الام</span>
                                                <input type="text" class="form-control" name="name_mother_purshes"
                                                    value="{{$client->name_mother_purshes}}" aria-describedby="input7">
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input8">رقم الهاتف 1</span>
                                                <input type="text" class="form-control" name="phone1"
                                                    value="{{$client->phone1}}" aria-describedby="input8">
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input9">رقم الهاتف 2</span>
                                                <input type="text" class="form-control" name="phone2"
                                                    value="{{$client->phone2}}" aria-describedby="input9">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input10">رقم البطاقة الوطنية</span>
                                                <input type="text" class="form-control" name="id_no"
                                                    value="{{$client->id_no}}" aria-describedby="input10">
                                            </div>
                                        </div>
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input11">محل اصدارها</span>
                                                <select class="form-control" aria-describedby="input11"
                                                    name="id_relese">
                                                    @php
                                                    $arr = array(
                                                    'كركوك',
                                                    'بغداد',
                                                    'البصرة',
                                                    'ميسان',
                                                    'ذي قار',
                                                    'الديوانية',
                                                    'المثنى',
                                                    'النجف الاشرف',
                                                    'كربلاء المقدسة',
                                                    'بابل',
                                                    'واسط',
                                                    'ديالى',
                                                    'صلاح الدين',
                                                    'نينوى',
                                                    'الانبار',
                                                    'اربيل',
                                                    'دهوك',
                                                    'سليمانية',
                                                    );
                                                    @endphp
                                                    <option value="">اختر...</option>
                                                    @foreach ($arr as $item)
                                                    <option value="{{$item}}" {{$client->id_relese == $item ? 'selected'
                                                        : ''}}>{{$item}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input12">تاريخ الاصدار</span>
                                                <input type="date" class="form-control" name="id_relese_date"
                                                    value="{{$client->id_relese_date}}" aria-describedby="input12">
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input13">العنوان</span>
                                                <input type="text" class="form-control" name="address"
                                                    value="{{$client->address}}" aria-describedby="input13">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-2 appendType">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input14">نوع البيع</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" disabled
                                                    value="{{$client->sold_type}}" aria-describedby="input14">
                                                @else
                                                <select class="form-control changetypesold" name="sold_type"
                                                    aria-describedby="input14">
                                                    <option value="">اختر...</option>
                                                    <option value="نقداً" {{$client->sold_type == 'نقداً' ? 'selected' :
                                                        ''}}>نقداً</option>
                                                    <option value="اقساط" {{$client->sold_type == 'اقساط' ? 'selected' :
                                                        ''}}>اقساط</option>
                                                    <option value="دفعات محددة" {{$client->sold_type == 'دفعات محددة' ?
                                                        'selected' : ''}}>دفعات محددة
                                                    </option>
                                                </select>
                                                @endif

                                            </div>
                                        </div>
                                        @if ($client->sold_type == 'نقداً')
                                        <div class="col-sm-2 append1">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input15">مبلغ البيع</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->sold_price}}"
                                                    aria-describedby="input15" data-type="currency" disabled>
                                                @else
                                                <input type="text" class="form-control" name="sold_price"
                                                    value="{{$client->sold_price}}" aria-describedby="input15"
                                                    data-type="currency" required>
                                                @endif

                                            </div>
                                        </div>
                                        <div class="col-sm-3 append1">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input16">قيمة المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_price}}"
                                                    aria-describedby="input16" data-type="currency" disabled>
                                                @else
                                                <input type="text" class="form-control" name="begen_price"
                                                    value="{{$client->begen_price}}" aria-describedby="input16"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append1">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input17">تاريخ المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_date}}"
                                                    aria-describedby="input17" disabled>
                                                @else
                                                <input type="date" class="form-control" name="begen_date"
                                                    value="{{$client->begen_date}}" aria-describedby="input17" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append1">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input18">مبلغ تسليم المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_prise}}" disabled>
                                                @else
                                                <input type="text" class="form-control" name="delvery_key_prise"
                                                    value="{{$client->delvery_key_prise}}" aria-describedby="input18"
                                                    data-type="currency" required>
                                                @endif

                                            </div>
                                        </div>
                                        <div class="col-sm-3 append1">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input19">تاريخ تسليم المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input19"
                                                    disabled>
                                                @else
                                                <input type="date" class="form-control" name="delvery_key_date"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input19"
                                                    required>
                                                @endif
                                            </div>
                                        </div>
                                        @elseif ($client->sold_type == 'اقساط')

                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input15">مبلغ البيع</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->sold_price}}"
                                                    aria-describedby="input15" disabled>
                                                @else
                                                <input type="text" class="form-control" name="sold_price"
                                                    value="{{$client->sold_price}}" aria-describedby="input15"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input16">قيمة المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_price}}"
                                                    aria-describedby="input16" disabled>
                                                @else
                                                <input type="text" class="form-control" name="begen_price"
                                                    value="{{$client->begen_price}}" aria-describedby="input16"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input177">تاريخ المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_date}}"
                                                    aria-describedby="input17" disabled>
                                                @else
                                                <input type="date" class="form-control" name="begen_date"
                                                    value="{{$client->begen_date}}" aria-describedby="input177"
                                                    required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input17">مبلغ تسليم المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_prise}}" aria-describedby="input17"
                                                    disabled>
                                                @else
                                                <input type="text" class="form-control" name="delvery_key_prise"
                                                    value="{{$client->delvery_key_prise}}" aria-describedby="input17"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input18">عدد الاشهر</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->months_no}}"
                                                    aria-describedby="input18" disabled>
                                                @else
                                                <select class="form-control" name="months_no" aria-describedby="input18"
                                                    required>
                                                    <option value="">اختر...
                                                    </option>
                                                    @for ($i = 1; $i <= 240; $i++) <option value="{{$i}}"
                                                        {{$i==$client->months_no ? 'selected' : ''}}>{{$i}}
                                                        </option>
                                                        @endfor
                                                </select>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input1888">عدد السنوات</span>
                                                <select class="form-control" name="years_no"
                                                    aria-describedby="input1888" required>
                                                    <option value="">اختر...</option>
                                                    @for ($i = 1; $i <= 20; $i++) <option value="{{$i}}" {{$i==$client->
                                                        years_no ? 'selected' : ''}}>{{$i}}
                                                        </option>
                                                        @endfor

                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-4 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input19">قيمة الدفع السنوي</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" aria-describedby="input19"
                                                    value="{{$client->annual_price}}" disabled>
                                                @else
                                                <input type="text" class="form-control" name="annual_price"
                                                    aria-describedby="input19" value="{{$client->annual_price}}"
                                                    data-type="currency" required>
                                                @endif

                                            </div>
                                        </div>
                                        <div class="col-sm-4 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input19">قيمة الدفع الشهري</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->monthly_price}}" aria-describedby="input19"
                                                    disabled>
                                                @else
                                                <input type="text" class="form-control" name="monthly_price"
                                                    value="{{$client->monthly_price}}" readonly
                                                    aria-describedby="input19" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input20">تاريخ القسط الاول</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->first_installment_date}}"
                                                    aria-describedby="input20" disabled>
                                                @else
                                                <input type="date" class="form-control" name="first_installment_date"
                                                    value="{{$client->first_installment_date}}"
                                                    aria-describedby="input20" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input21">تاريخ تسليم المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input21"
                                                    disabled>
                                                @else
                                                <input type="date" class="form-control" name="delvery_key_date"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input21"
                                                    required>
                                                @endif
                                            </div>
                                        </div>
                                        @if ($installments)
                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input100">المبالغ المضافة</span>
                                                <input type="text" class="form-control" value="{{$client->addprice}}"
                                                    aria-describedby="input100" disabled>
                                            </div>
                                        </div>
                                        @else
                                        <div class="col-sm-2 append2">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input100">المبالغ المضافة</span>
                                                <select class="form-control" name="addprice" aria-describedby="input100"
                                                    required>
                                                    <option value="كلا" {{$client->addprice == 'كلا' ? 'selected' :
                                                        ''}}>كلا</option>
                                                    <option value="نعم" {{$client->addprice == 'نعم' ? 'selected' :
                                                        ''}}>نعم</option>
                                                </select>
                                            </div>
                                        </div>
                                        @endif
                                        <div class="appendaddprice append2 row">
                                            @if ($client->addprice == 'نعم')
                                            @for ($i = 1; $i <= $client->years_no; $i++)

                                                @php
                                                switch ($i) {
                                                case '1':
                                                $val = $client->prise_add;
                                                break;
                                                case '2':
                                                $val = $client->prise_add2;
                                                break;
                                                case '3':
                                                $val = $client->prise_add3;
                                                break;
                                                case '4':
                                                $val = $client->prise_add4;
                                                break;
                                                case '5':
                                                $val = $client->prise_add5;
                                                break;
                                                case '6':
                                                $val = $client->prise_add6;
                                                break;
                                                case '7':
                                                $val = $client->prise_add7;
                                                break;
                                                case '8':
                                                $val = $client->prise_add8;
                                                break;
                                                case '9':
                                                $val = $client->prise_add9;
                                                break;
                                                case '10':
                                                $val = $client->prise_add10;
                                                break;
                                                default:
                                                break;
                                                }
                                                @endphp
                                                @if ($installments)
                                                <div class="col-sm-3">
                                                    <div class="input-group mb-3">
                                                        <span class="input-group-text">قيمة المبلغ المضاف
                                                            للسنة {{$i}}
                                                        </span>
                                                        <input type="text" class="form-control" value="{{$val}}"
                                                            disabled>
                                                    </div>
                                                </div>
                                                @else
                                                <div class="col-sm-3">
                                                    <div class="input-group mb-3">
                                                        <span class="input-group-text">قيمة المبلغ المضاف
                                                            للسنة {{$i}}
                                                        </span>
                                                        <input type="text" class="form-control" value="{{$val}}"
                                                            name="prise_add{{$i}}" data-type="currency" required>
                                                    </div>
                                                </div>
                                                @endif
                                                @endfor

                                                @endif
                                        </div>
                                        @elseif ($client->sold_type == 'دفعات محددة')
                                        <div class="col-sm-2 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input15">مبلغ البيع</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->sold_price}}"
                                                    aria-describedby="input15" disabled>
                                                @else
                                                <input type="text" class="form-control" name="sold_price"
                                                    value="{{$client->sold_price}}" aria-describedby="input15"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input16">قيمة المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_price}}"
                                                    aria-describedby="input16" disabled>
                                                @else
                                                <input type="text" class="form-control" name="begen_price"
                                                    value="{{$client->begen_price}}" aria-describedby="input16"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input177">تاريخ
                                                    المقدمة</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->begen_date}}"
                                                    aria-describedby="input17" disabled>
                                                @else
                                                <input type="date" class="form-control" name="begen_date"
                                                    value="{{$client->begen_date}}" aria-describedby="input177"
                                                    required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input17">مبلغ تسليم
                                                    المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_prise}}" aria-describedby="input17"
                                                    disabled>
                                                @else
                                                <input type="text" class="form-control" name="delvery_key_prise"
                                                    value="{{$client->delvery_key_prise}}" aria-describedby="input17"
                                                    data-type="currency" required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-3 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input18">تاريخ تسليم
                                                    المفتاح</span>
                                                @if ($installments)
                                                <input type="text" class="form-control"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input18"
                                                    disabled>
                                                @else
                                                <input type="date" class="form-control" name="delvery_key_date"
                                                    value="{{$client->delvery_key_date}}" aria-describedby="input18"
                                                    required>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-sm-2 append3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input19">عدد الدفعات</span>
                                                @if ($installments)
                                                <input type="text" class="form-control" value="{{$client->payments_no}}"
                                                    aria-describedby="input19" disabled>
                                                @else
                                                <select class="form-control input20" name="payments_no"
                                                    aria-describedby="input19" required>
                                                    <option value="">اختر...</option>
                                                    <?php
                                            for ($i = 1; $i <= 20; $i++) {
                                                echo '<option value="' . $i . '" '.($client->payments_no == $i ? 'selected': '').'>' . $i . '</option>';
                                            }
                                        ?>
                                                </select>
                                                @endif

                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="appendinputs">
                                        @foreach ($payments as $payment)
                                        <div class="row mb-3 ">
                                            <div class="col-sm-2">
                                                <div class="input-group mb-3">
                                                    <span class="input-group-text" id="input23">مبلغ الدفعة</span>
                                                    @if ($installments)
                                                    <input type="text" class="form-control"
                                                        value="{{$payment->payment_price}}" aria-describedby="input23"
                                                        disabled>
                                                    @else
                                                    <input type="text" class="form-control" name="payment_price[]"
                                                        value="{{$payment->payment_price}}" aria-describedby="input23"
                                                        data-type="currency" required>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="input-group mb-3">
                                                    <span class="input-group-text" id="input24">تاريخ تسليم
                                                        المفتاح</span>
                                                    @if ($installments)
                                                    <input type="date" class="form-control"
                                                        value="{{$payment->payment_date}}" aria-describedby="input24"
                                                        disabled>
                                                    @else
                                                    <input type="date" class="form-control" name="payment_date[]"
                                                        value="{{$payment->payment_date}}" aria-describedby="input24"
                                                        required>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <div class="input-group mb-3">
                                                <span class="input-group-text" id="input22">تحميل الملفات</span>
                                                <input type="file" class="form-control" name="files[]" multiple
                                                    aria-describedby="input22" accept=".jpg,.png,.pdf,.doc,.docx,.xlsx">
                                                <input type="hidden" value="{{$client->files}}" name="files_old">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary">تحديث</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection