@extends('admin.project.layouts.app2')

@section('title', 'دفتر الايداع')
@section('content')
<div class="col-md-12 col-lg-12">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="card-body">
                    <form class="row mb-1" id="search_form" data-url-show="{{route('project.deposits.show')}}">

                        <div class="col-2">
                            <div style="text-align: center;">
                                <input class="form-control" type="text" id="search" placeholder="المودع" />
                            </div>
                        </div>
                        <div class="col-2">
                            <div style="text-align: center;">
                                <input class="form-control" type="text" id="search2" placeholder="التفاصيل" />
                            </div>
                        </div>

                        <div class="col-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1">من</span>
                                <input type="date" class="form-control" aria-describedby="basic-addon1" id="search3">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon2">الى</span>
                                <input type="date" class="form-control" aria-describedby="basic-addon2" id="search4">
                            </div>
                        </div>
                    </form>
                    <div class="mb-1">
                        <button type="button" class="btn btn-custom">
                            <b>مجموع المبالغ:</b> <span id="sumAmount">0</span>
                        </button>
                        <button type="button" class="btn btn-custom printReportDaily"
                            data-url-print-daily="{{route('project.deposits.printreportdaily')}}">
                            طباعة مستند يومي
                        </button>
                        <button type="button" class="btn btn-dark printReport"
                            data-url-print="{{route('project.deposits.printreport')}}">
                            طباعة حسب الفرز
                        </button>
                    </div>
                    <div id="dynamic_table" class="text-center">

                    </div>
                    <div style="clear:both"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="offcanvas offcanvas-start w-50" data-bs-backdrop="static" tabindex="-1" id="EditStaticBackdrop"
    aria-labelledby="EditStaticBackdropLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="EditStaticBackdropLabel">تعديل</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form id="ُEditFormDeposit" method="post" data-edit-url="{{route('project.deposits.update')}}">
            @csrf
            <input type="hidden" id="geteditId" name="id">
            <label class="form-label" for="input1">تاريخ الايداع</label>
            <div style="display: flex;align-items: center;">
                <div class="me-5 ms-3 " id="DivIdToPrint">
                    <div class="input-group mb-3">
                        <label class="input-group-text" for="input1">اليوم</label>
                        <select class="form-select" id="input1" style="width: 95px;" name="day" required>
                            @for ($i = 1; $i <= 31; $i++) <option value="{{$i}}">{{$i}}</option>
                                @endfor
                        </select>
                    </div>
                </div>
                <div class="me-5 ms-3">
                    <div class="input-group mb-3">
                        <label class="input-group-text" for="input2">الشهر</label>
                        <select class="form-select" id="input2" style="width: 95px;" name="month" required>
                            <option value="">اختر...</option>
                            @for ($i = 1; $i <= 12; $i++) <option value="{{$i}}">{{$i}}</option>
                                @endfor
                        </select>
                    </div>
                </div>
                <div class="ms-3">
                    <div class="input-group mb-3">
                        <label class="input-group-text" for="input3">السنة</label>
                        <select class="form-select" id="input3" style="width: 150px;" name="year" required>
                            <option value="">اختر...</option>
                            @for ($i = 2023; $i <= date("Y"); $i++) <option value="{{$i}}">{{$i}}
                                </option>
                                @endfor
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group mt-3 row">
                <div class="col-5">
                    <label for="input5" class="form-label">المودع</label>
                    <input class="form-control" name="deposit_name" id="input5" required>
                </div>

                <div class="col-4">
                    <label for="input7" class="form-label">مبلغ الايداع</label>
                    <input class="form-control text-dark" style="font-weight:bold" type="text" id="input7"
                        name="deposit_amount" data-type="currency" required>
                </div>
            </div>
            <div class="form-group mt-3 row">
                <label for="input8" class="form-label">التفاصيل</label>
                <input class="form-control" type="text" id="input8" name="deposit_details" required>
            </div>
            <div class="d-flex justify-content-between align-items-baseline">
                <div class="form-group mt-30 " style="width: 60px;">
                    <label for="input9" class="btn btn-custom filupp">
                        <i class="fa-solid fa-upload"></i>
                        <input type="file" name="deposit_file[]" multiple class="d-none" id="input9"
                            accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xlsx" />
                    </label>
                    <input type="hidden" name="deposit_file_old" id="input10" class="d-none" />
                </div>
                <div>
                    <span class="filupp-file-name js-value ms-5"></span>
                    <button type="button" class="btn btn-danger btn-sm js-value-del ms-5">حذف الملفات</button>
                </div>
                <div class="form-group mt-3 text-end">
                    <button type="submit" class="btn btn-success btn-custom btn-send">تحديث</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection