@extends('admin.layouts.app')

@section('title', 'الملف الشخصي')
@section('content')
<div class="conatiner-fluid content-inner py-0">
    <div>
        <div class="row">
            <div class="col-xl-3 col-lg-4">
                <div class="card text-center">
                    <div class="card-header">
                        <div class="header-title">
                            <h6 class="card-title">{{\Auth::user()->name}}</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="profile-img-edit position-relative">
                                <img src="{{asset('public/assets/images/avatars/01.png')}}" alt="profile-pic"
                                    class="theme-color-default-img profile-pic rounded avatar-100">
                                <img src="{{asset('public/assets/images/avatars/avtar_1.png')}}" alt="profile-pic"
                                    class="theme-color-purple-img profile-pic rounded avatar-100">
                                <img src="{{asset('public/assets/images/avatars/avtar_2.png')}}" alt="profile-pic"
                                    class="theme-color-blue-img profile-pic rounded avatar-100">
                                <img src="{{asset('public/assets/images/avatars/avtar_4.png')}}" alt="profile-pic"
                                    class="theme-color-green-img profile-pic rounded avatar-100">
                                <img src="{{asset('public/assets/images/avatars/avtar_5.png')}}" alt="profile-pic"
                                    class="theme-color-yellow-img profile-pic rounded avatar-100">
                                <img src="{{asset('public/assets/images/avatars/avtar_3.png')}}" alt="profile-pic"
                                    class="theme-color-pink-img profile-pic rounded avatar-100">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">صلاحية المستخدم</label>
                            <h6>{{\Auth::user()->rule_name}}</h6>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-9 col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <div class="header-title">
                            <h4 class="card-title">المعلومات العامة</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="new-user-info">
                            <form action="{{route('admin.profile.update')}}" method="POST">
                                @csrf
                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label" for="fname">الاسم الكامل:</label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                            id="fname" name="name" value="{{\Auth::user()->name}}">
                                        @error('name')
                                        <p class="text-danger">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label" for="mobno">رقم الهاتف:</label>
                                        <input type="text"
                                            class="form-control @error('mobile_number') is-invalid @enderror" id="mobno"
                                            name="mobile_number" value="{{\Auth::user()->mobile_number}}">
                                        @error('mobile_number')
                                        <p class="text-danger">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                                <hr>
                                <h5 class="mb-3">معلومات الدخول</h5>
                                <div class="row">
                                    <div class="form-group col-md-12">
                                        <label class="form-label" for="uname">اسم المتسخدم:</label>
                                        <input type="text" class="form-control @error('username') is-invalid @enderror"
                                            id="uname" name="username" value="{{\Auth::user()->username}}">
                                        @error('username')
                                        <p class="text-danger">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label" for="pass">كلمة السر:</label>
                                        <input type="password"
                                            class="form-control @error('password') is-invalid @enderror" id="pass"
                                            name="password" placeholder="اترك الحقل فارغ اذا لم ترغب بتغيرها"
                                            autocomplete="new-password">
                                        @error('password')
                                        <p class="text-danger">{{ $message }}</p>
                                        @enderror
                                        <input type="hidden" name="old_password" value="{{\Auth::user()->password}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label" for="rpass">اعادة كتابة كلمة السر:</label>
                                        <input type="password"
                                            class="form-control @error('password_confirmation') is-invalid @enderror"
                                            id="rpass" name="password_confirmation" autocomplete="new-password">
                                        @error('password_confirmation')
                                        <p class="text-danger">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">حفظ المعلومات</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection