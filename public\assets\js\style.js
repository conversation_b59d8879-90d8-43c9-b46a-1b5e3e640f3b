$(function () {
    "use strict";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    $(document).keypress(function (event) {
        if (event.which == "13") {
            event.preventDefault();
        }
    });
    $("[data-searchable='searchable']").select2({
        dir: "rtl",
        theme: "bootstrap4",
        language: "ar",
        placeholder: "اختر...",
    });
    // $("#newClient").validate();
    // $.extend($.validator.messages, {
    //     required: "هذا الحقل مطلوب",
    //     minlength: "يجب ان يكون رقم الهاتف 11 رقم",
    //     maxlength: "يجب ان يكون رقم الهاتف 11 رقم",
    //     accept: "تنسيق الملف غير مدعوم!",
    // });
    load_doors();
    function load_doors() {
        $.ajax({
            url: $("#getDoorsUrl").val(),
            method: "POST",
            dataType: "json",
            success: function (data) {
                $("#dynamic_data_doors_type1").html(data.table_data_type1);
                $("#dynamic_data_doors_type2").html(data.table_data_type2);

                $("[name='deposit_door']").html(
                    '<option value="">باب الايداع</option>' +
                        data.select_data_type1
                );
                $("[name='expense_door']").html(
                    '<option value="">باب الصرف</option>' +
                        data.select_data_type2
                );
            },
            error: function (data) {
                console.log(data);
            },
        });
    }
    load_amounts();
    function load_amounts() {
        $.ajax({
            url: $("#getAmountUrl").val(),
            method: "GET",
            dataType: "json",
            success: function (data) {
                $(".totalAmount").text(data.total);
                $(".totalDeposits").text(data.deposit);
                $(".totalExpenses").text(data.expense);
                $(".totalCustomers").text(data.customers);
            },
            error: function (data) {
                console.log(data);
            },
        });
    }
    // Edit
    $(document).on("click", ".editdoor", function (e) {
        $("#doorname").val($(this).attr("data-door-name"));
        $("#doortype").val($(this).attr("data-door-type"));
        $("[name='doorid']").val($(this).attr("id"));
        $("#addDoors .btn-send").text("تحديث");
    });
    // Add Doors
    $("#addDoors").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $("#addDoors").attr("data-adddoors-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $("#addDoors").trigger("reset");
                    load_doors();
                } else {
                    $.alert({
                        title: "تنبيه!",
                        content: "هذا الاسم قد اضيف مسبقاً!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                }
            },
            error: function (data) {
                console.log(data);
            },
            complete: function () {
                $(".btn-send").html("اضافة");
                $(".btn-send").removeAttr("disabled");
                $("[name='doorid']").val("");
                $("#addDoors .btn-send").text("اضافة");
            },
        });
    });
    // Delete Door
    $(document).on("click", ".deletedoor", function () {
        var deleteurl = $(this);
        var id = deleteurl.attr("id");
        $.confirm({
            title: "تأكيد!",
            content: "هل انت متأكد؟",
            rtl: true,
            buttons: {
                نعم: function () {
                    $.ajax({
                        method: "POST",
                        url: deleteurl.attr("data-url-delete-door"),
                        data: {
                            id: id,
                        },
                        success: function () {
                            load_doors();
                        },
                    });
                },
                كلا: function () {},
            },
        });
    });
    // Check Upload file
    $("#input9").on("change", function () {
        var fileInput = $(this);
        var files = fileInput[0].files;

        // Check if any files were selected
        if (files.length > 0) {
            var validExtensions = [
                "jpg",
                "jpeg",
                "png",
                "pdf",
                "doc",
                "docx",
                "xlsx",
            ];

            // Iterate over the selected files
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                var fileExtension = file.name.split(".").pop().toLowerCase();

                // Check if the file extension is valid
                if (validExtensions.indexOf(fileExtension) === -1) {
                    $.alert({
                        title: "تنبيه!",
                        content: "نوع ملف " + file.name + " غير معروف",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    fileInput.val(""); // Clear the file input
                    $(".js-value").text("");
                    return;
                }
                if (file.size > 10485760) {
                    $.alert({
                        title: "تنبيه!",
                        content: "الحجم المسموح به هو 10MB!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    fileInput.val(""); // Clear the file input
                    $(".js-value").text("");
                    return;
                }
                $(".js-value").text(i + 1 + " " + (i == 0 ? "ملف" : "ملفات"));
            }
        }
    });
    // End Check Upload file
    $("#input4").on("change", function () {
        $("[name='deposit_amount']").val("");
        if ($(this).val() != "") {
            if ($(this).val() == "1") {
                if ($(this).attr("data-type-door") == "1") {
                    $("#input5").attr("name", "deposit_name");
                } else {
                    $("#input5").attr("name", "expense_name");
                }
                $("#input5").attr("required", "required");
                $("#input5").removeAttr("style");
                $(".select2").removeAttr("style");
                $("#input55").removeAttr("name");
                $("#input55").removeAttr("required");
                $("#input55").css("display", "none");
                $('[name="deposit_type"]').html(
                    '<option value="">اختر...</option>' +
                        '<option value="نقداً">نقداً</option>' +
                        '<option value="صك">صك</option>' +
                        '<option value="اضافة الى المحفظة">اضافة الى المحفظة</option>' +
                        '<option value="سحب من المحفظة">سحب من المحفظة</option>'
                );
            } else {
                $("#input5").css("display", "none");
                $(".select2").css("display", "none");
                $("#input5").removeAttr("name");
                $("#input5").removeAttr("required");
                $("#input55").removeAttr("style");
                if ($(this).attr("data-type-door") == "1") {
                    $("#input55").attr("name", "deposit_name");
                } else {
                    $("#input55").attr("name", "expense_name");
                }
                $("#input55").attr("required", "required");
                $(".appendinstallments").html("");
                $("#input7").val("");
                $("#input5").val(null).trigger("change");
                $('[name="deposit_type"]').html(
                    '<option value="">اختر...</option>' +
                        '<option value="نقداً">نقداً</option>' +
                        '<option value="صك">صك</option>'
                );
                $("#input6666").val("0");
                $("[name='deposit_amount']").removeAttr("readonly");
            }
            $(".hiddenInput").removeAttr("style");
        } else {
            $(".hiddenInput").css("display", "none");
        }
    });
    $('[name="deposit_type"]').on("change", function () {
        if ($("#input4").val() == "1") {
            $("[name='deposit_amount']").val("");
            $("[name='price_name']").prop("checked", false);
            if ($(this).val() == "اضافة الى المحفظة") {
                $("[name='deposit_amount']").removeAttr("readonly");
                $(".appendinstallments").attr("style", "display:none");
                $("[name='price_name']").prop("checked", false);
            } else {
                $("[name='deposit_amount']").attr("readonly", "readonly");
                $(".appendinstallments").removeAttr("style");
            }
        }
    });
    // Print ReportDaily
    $(".printReportDaily").on("click", function () {
        $.ajax({
            method: "POST",
            url: $(this).attr("data-url-print-daily"),
            success: function (data) {
                if (data == "empty") {
                    $.alert({
                        title: "تنبيه!",
                        content: "لا يوجد بيانات مضافة اليوم لعرضها!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                } else {
                    let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=900,height=600,left=100,top=100`;
                    var newWin2 = window.open("", "Print-Window", params);

                    newWin2.document.open();

                    newWin2.document.write(
                        '<html dir="rtl"><head><meta charset="utf-8" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous"/>' +
                            '<link rel="stylesheet" href="https://www.obe.seio.uk/public/assets/css/style.css"/> ' +
                            '<style>h6 { font-size: 14px;} .table tbody tr td, .table thead tr th { font-size: 10px!important;}.table tbody tr td {background-color: #00000000;}</style></head><body onload="window.print();">' +
                            '<div style="">' +
                            '<div style="margin-top: 10px">' +
                            data +
                            "</div>" +
                            "</div>" +
                            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>' +
                            "</body>" +
                            "</html>"
                    );

                    newWin2.document.close();

                    // setTimeout(function () {
                    //     newWin2.close();
                    // }, 10);
                }
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Print Report
    $(".printReport").on("click", function () {
        $.ajax({
            method: "POST",
            url: $(this).attr("data-url-print"),
            method: "GET",
            data: {
                query: $("#search").val(),
                query2: $("#search2").val(),
                query3: $("#search3").val(),
                query4: $("#search4").val(),
                query5: $("#search5").val(),
            },
            success: function (data) {
                if (data == "empty") {
                    $.alert({
                        title: "تنبيه!",
                        content: "لا يوجد بيانات مضافة اليوم لعرضها!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                } else {
                    let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=900,height=600,left=100,top=100`;
                    var newWin2 = window.open("", "Print-Window", params);

                    newWin2.document.open();

                    newWin2.document.write(
                        '<html dir="rtl"><head><meta charset="utf-8" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous"/>' +
                            '<link rel="stylesheet" href="https://www.obe.seio.uk/public/assets/css/style.css"/> ' +
                            '<style>h6 { font-size: 14px;} .table tbody tr td, .table thead tr th { font-size: 9px!important;}.table tbody tr td {background-color: #00000000;}</style></head><body onload="window.print();">' +
                            "<div>" +
                            '<div style="margin-top: 10px">' +
                            data +
                            "</div>" +
                            "</div>" +
                            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>' +
                            "</body>" +
                            "</html>"
                    );

                    newWin2.document.close();

                    // setTimeout(function () {
                    //     newWin2.close();
                    // }, 10);
                }
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Check House Number If Exist
    $("[name='house_no']").on("change", function () {
        var houseno = $(this);
        $.ajax({
            url: houseno.attr("data-check-url"),
            method: "get",
            data: {
                houseno: houseno.val(),
                checkid: houseno.attr("data-check-id"),
            },
            success: function (data) {
                if (data == "exist") {
                    $.alert({
                        title: "تنبيه!",
                        content: "رقم الدار مسجل مسبقاً!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    houseno.val("");
                }
            },
        });
    });
    // Add Deposit
    $(".newDeposit").on("submit", function (e) {
        e.preventDefault();

        var formData = new FormData(this);
        formData.append(
            "deposit_door_name",
            $("#input4 option:selected").text()
        );
        formData.append(
            "client_name",
            $("#input5 option:selected").attr("data-client-name")
        );
        formData.append(
            "house_no",
            $("#input5 option:selected").attr("data-house-no")
        );
        $.ajax({
            data: formData,
            url: $(".newDeposit").attr("data-form-deposit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    var paid = "",
                        unpaid = "",
                        total = "";
                    $.ajax({
                        url: $("#input5").attr("data-url-fetch-installments"),
                        method: "POST",
                        data: {
                            id: $("#input5").val(),
                        },
                        dataType: "json",
                        success: function (data) {
                            paid = data.installments_paid;
                            unpaid = data.installments_unpaid;
                            total = data.total;
                        },
                    });
                    load_amounts();
                    $(".js-value").text("");
                    if ($("#input4").val() == "1") {
                        $.confirm({
                            theme: "supervan",
                            title: "تأكيد!",
                            content:
                                "تم اضافة الايداع بنجاح <br> هل ترغب بطباعة سند الايداع؟",
                            rtl: true,
                            buttons: {
                                نعم: function () {
                                    let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=900,height=600,left=100,top=100`;
                                    var newWin = window.open(
                                        "",
                                        "Print-Window",
                                        params
                                    );

                                    newWin.document.open();

                                    newWin.document.write(
                                        '<html dir="rtl"><head><meta charset="utf-8" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous"/>' +
                                            '<link rel="stylesheet" href="https://www.obe.seio.uk/public/assets/css/style.css"/> ' +
                                            '<style>h6 { font-size: 14px;}</style></head><body onload="window.print();" style="background-image: url(https://www.obe.seio.uk/public/assets/images/frameborder.webp);background-repeat: no-repeat;background-size: 100%;background-position: center;">' +
                                            '<div class="container">' +
                                            '<div style="margin-top: 300px">' +
                                            "<h6>التاريخ: " +
                                            $("#input1").val() +
                                            "/" +
                                            $("#input2").val() +
                                            "/" +
                                            $("#input3").val() +
                                            "</h6>" +
                                            "<h6>رقم السند: " +
                                            data["id"] +
                                            "</h6>" +
                                            "</div>" +
                                            '<div class="mt-5">' +
                                            "<h6>اسم العميل: " +
                                            $("#input5 option:selected").data(
                                                "client-name"
                                            ) +
                                            "</h6>" +
                                            "<h6>نوع البيع: " +
                                            $("#input5 option:selected").data(
                                                "client-sold-type"
                                            ) +
                                            ' <span class="ms-3">رقم الدار: ' +
                                            $("#input5 option:selected").attr(
                                                "data-house-no"
                                            ) +
                                            "</span></h6>" +
                                            "</div>" +
                                            '<div class="mt-5">' +
                                            "<h6>المبلغ: " +
                                            $("#input7").val() +
                                            "</h6>" +
                                            "<h6>" +
                                            convert_number(
                                                $("#input7").val(),
                                                "male"
                                            ) +
                                            " دينار عراقي </h6>" +
                                            "</div>" +
                                            '<div class="mt-5">' +
                                            "<h6>التفاصيل:</h6>" +
                                            "<h6>" +
                                            $("#input8").val() +
                                            "</h6>" +
                                            "</div>" +
                                            '<div class="mt-5">' +
                                            "<h6>عدد الاقساط المدفوعة: (" +
                                            paid +
                                            ") | عدد الاقساط المتبقي: (" +
                                            unpaid +
                                            ")</h6>" +
                                            "</div>" +
                                            '<div class="mt-2">' +
                                            "<h6>المبلغ المتبقي: " +
                                            total +
                                            "</h6>" +
                                            "</div>" +
                                            '<div style="margin-top: 130px">' +
                                            '<div class="row">' +
                                            '<div class="col-6 ms-5"><h6>توقيع المحاسب</h6></div>' +
                                            "</div>" +
                                            "</div>" +
                                            "</div>" +
                                            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>' +
                                            "</body>" +
                                            "</html>"
                                    );

                                    newWin.document.close();

                                    // setTimeout(function () {
                                    //     newWin.close();
                                    // }, 10);
                                    $(".newDeposit").trigger("reset");
                                    $(".hiddenInput").css("display", "none");
                                    $(".appendinstallments").html("");
                                    $("#input5").val(null).trigger("change");
                                },
                                كلا: function () {
                                    $(".newDeposit").trigger("reset");
                                    $(".hiddenInput").css("display", "none");
                                    $(".appendinstallments").html("");
                                    $("#input5").val(null).trigger("change");
                                },
                            },
                        });
                    } else {
                        $.toast({
                            text: "تم اضافة سند الايداع بنجاح", // Text that is to be shown in the toast
                            heading: "نجاح", // Optional heading to be shown on the toast
                            icon: "success", // Type of toast icon
                            showHideTransition: "fade", // fade, slide or plain
                            allowToastClose: true, // Boolean value true or false
                            hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                            stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                            position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                            textAlign: "right", // Text alignment i.e. left, right or center
                            loader: true, // Whether to show loader or not. True by default
                            loaderBg: "#9EC600", // Background color of the toast loader
                            beforeShow: function () {}, // will be triggered before the toast is shown
                            afterShown: function () {}, // will be triggered after the toat has been shown
                            beforeHide: function () {}, // will be triggered before the toast gets hidden
                            afterHidden: function () {}, // will be triggered after the toast has been hidden
                        });

                        $(".newDeposit").trigger("reset");
                        $(".hiddenInput").css("display", "none");
                        $(".appendinstallments").html("");
                        $("#input5").val(null).trigger("change");
                    }
                }
            },
            complete: function () {
                $(".btn-send").html("اضافة");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (d) {
                console.log(d["responseJSON"]["message"]);
                $.alert({
                    title: "تنبيه!",
                    content: d["responseJSON"]["message"],
                    type: "red",
                    typeAnimated: true,
                    rtl: true,
                });
            },
        });
    });
    // Print ReportDaily
    $(document).on("click", ".edit", function () {
        if ($(this).attr("data-type-form") == 1) {
            $("#geteditId").val($(this).attr("id"));
            $("#input1").val($(this).attr("data-day"));
            $("#input2").val($(this).attr("data-month"));
            $("#input3").val($(this).attr("data-year"));
            if ($(this).attr("deposit-door-name") != "عملاء") {
                $("#input5").attr("name", "deposit_name");
                $("#input5").attr("required", "required");
                $("#input5").removeAttr("disabled");
                $("#input5").val($(this).attr("data-deposit-name"));
            } else {
                $("#input5").removeAttr("name");
                $("#input5").removeAttr("required");
                $("#input5").attr("disabled", "disabled");
                $("#input5").val($(this).attr("deposit-house-no"));
                $("[name='clientId']").val($(this).attr("data-client-id"));
            }

            $(".input6").val($(this).attr("data-deposit-type"));
            $("#input6").val($(this).attr("data-deposit-type"));
            $("#input7").val($(this).attr("data-deposit-amount"));
            $("[name='old_deposit_amount']").val(
                $(this).attr("data-deposit-amount")
            );
            $("[name='deposit_door_name']").val(
                $(this).attr("deposit-door-name")
            );

            $("#input8").val($(this).attr("data-deposit-details"));
            $("#input10").val($(this).attr("data-deposit-file"));
            if ($(this).attr("data-deposit-file") != "") {
                var files = $(this).attr("data-deposit-file").split(",");
                $(".js-value").text(
                    files.length + " " + (files.length == 1 ? "ملف" : "ملفات")
                );
            } else {
                $(".js-value").text("");
            }

            $("#EditStaticBackdrop").offcanvas("show");
        } else {
            $("#geteditId2").val($(this).attr("id"));
            $(".input1").val($(this).attr("data-day"));
            $(".input2").val($(this).attr("data-month"));
            $(".input3").val($(this).attr("data-year"));
            $(".input4").val($(this).attr("data-expense-door"));
            $(".input5").val($(this).attr("data-expense-name"));
            $(".input7").val($(this).attr("data-expense-amount"));
            $(".input8").val($(this).attr("data-expense-details"));
            $(".input10").val($(this).attr("data-expense-file"));
            if ($(this).attr("data-expense-file") != "") {
                var files = $(this).attr("data-expense-file").split(",");
                $(".js-value").text(
                    files.length + " " + (files.length == 1 ? "ملف" : "ملفات")
                );
            } else {
                $(".js-value").text("");
            }

            $("#Edit2StaticBackdrop").offcanvas("show");
        }
    });
    $(document).on("click", ".js-value-del", function () {
        $("#input10").val("");
        $(".js-value").text("");
    });
    // Edit Deposit
    $("#ُEditFormDeposit").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $("#ُEditFormDeposit").attr("data-edit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $.toast({
                        text: "تم تحديث سند الايداع بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                    load_data(1);
                    $("#ُEditFormDeposit").trigger("reset");
                    $("#EditStaticBackdrop").offcanvas("hide");
                }
            },
            complete: function () {
                $(".btn-send").html("تحديث");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (data) {
                console.log(data);
            },
        });
    });

    // Add Expense
    $(".newExpense").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        formData.append(
            "expense_door_name",
            $("#input4 option:selected").text()
        );
        $.ajax({
            data: formData,
            url: $(".newExpense").attr("data-form-expense-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    load_amounts();
                    $(".js-value").text("");
                    $(".hiddenInput").css("display", "none");
                    $(".newExpense").trigger("reset");
                    $.toast({
                        text: "تم اضافة سند الايداع بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                }
            },
            error: function (data) {
                console.log(data);
            },
            complete: function () {
                $(".btn-send").html("اضافة");
                $(".btn-send").removeAttr("disabled");
            },
        });
    });
    // Edit Expense
    $("#ُEdit2FormDeposit").on("submit", function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajax({
            data: formData,
            url: $("#ُEdit2FormDeposit").attr("data-edit-url"),
            method: "POST",
            dataType: "json",
            contentType: false,
            cache: false,
            processData: false,
            beforeSend: function () {
                // setting a timeout
                $(".btn-send").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
                );
                $(".btn-send").attr("disabled", "disabled");
            },
            success: function (data) {
                if (data["state"] == "success") {
                    $.toast({
                        text: "تم تحديث سند الصرف بنجاح", // Text that is to be shown in the toast
                        heading: "نجاح", // Optional heading to be shown on the toast
                        icon: "success", // Type of toast icon
                        showHideTransition: "fade", // fade, slide or plain
                        allowToastClose: true, // Boolean value true or false
                        hideAfter: 3000, // false to make it sticky or number representing the miliseconds as time after which toast needs to be hidden
                        stack: 5, // false if there should be only one toast at a time or a number representing the maximum number of toasts to be shown at a time
                        position: "top-right", // bottom-left or bottom-right or bottom-center or top-left or top-right or top-center or mid-center or an object representing the left, right, top, bottom values

                        textAlign: "right", // Text alignment i.e. left, right or center
                        loader: true, // Whether to show loader or not. True by default
                        loaderBg: "#9EC600", // Background color of the toast loader
                        beforeShow: function () {}, // will be triggered before the toast is shown
                        afterShown: function () {}, // will be triggered after the toat has been shown
                        beforeHide: function () {}, // will be triggered before the toast gets hidden
                        afterHidden: function () {}, // will be triggered after the toast has been hidden
                    });
                    load_data(1);
                    $("#ُEdit2FormDeposit").trigger("reset");
                    $("#Edit2StaticBackdrop").offcanvas("hide");
                }
            },
            complete: function () {
                $(".btn-send").html("تحديث");
                $(".btn-send").removeAttr("disabled");
            },
            error: function (data) {
                console.log(data);
            },
        });
    });
    // Show Files
    $(document).on("click", ".showfiles", function () {
        $("#showFilesModal").modal("show");

        var arr = $(this).attr("data-get-files").split(","),
            listitem = [],
            url = $(this).attr("data-url");

        arr.forEach(function (ele, idx) {
            if (ele) {
                listitem.push(
                    '<a href="' +
                        url +
                        "/" +
                        ele +
                        '" target="_blank" class="list-group-item list-group-item-info">الملف ' +
                        (idx + 1) +
                        "</a>"
                );
            }
        });

        $(".showFiles .list-group").html("");
        $(".showFiles .list-group").html(listitem);
    });

    /* *******************************************
     *********************************************
     *********************************************
     ************* Delete Record Ajax ************
     *********************************************
     *********************************************
     *********************************************/
    $(document).on("click", ".delete", function () {
        var deleteurl = $(this),
            id = deleteurl.attr("id"),
            clientId = deleteurl.attr("data-client-id"),
            depositType = deleteurl.attr("data-deposit-type"),
            depositAmount = deleteurl.attr("data-deposit-amount");
        $.confirm({
            title: "تأكيد!",
            content: "هل انت متأكد؟",
            rtl: true,
            buttons: {
                نعم: function () {
                    $.ajax({
                        method: "POST",
                        url: deleteurl.attr("data-url-delete"),
                        data: {
                            id: id,
                            clientId: clientId,
                            depositType: depositType,
                            depositAmount: depositAmount,
                        },
                        success: function (data) {
                            if (data == "success") {
                                load_data(1);
                            } else {
                                $.alert({
                                    title: "تنبيه!",
                                    content: "لا يمكن مسح هذه الايداع!",
                                    type: "red",
                                    typeAnimated: true,
                                    rtl: true,
                                });
                            }
                        },
                        error: function (d) {
                            console.log(d);
                        },
                    });
                },
                كلا: function () {},
            },
        });
    });
    /* *******************************************
     *********************************************
     *********************************************
     ************* Delete Client Ajax ************
     *********************************************
     *********************************************
     *********************************************/
    $(document).on("click", ".deleteclient", function () {
        var deleteurl = $(this);
        var id = deleteurl.attr("id");
        $.confirm({
            title: "تأكيد!",
            content: "هل انت متأكد؟",
            rtl: true,
            buttons: {
                نعم: function () {
                    $.ajax({
                        method: "POST",
                        url: deleteurl.attr("data-url-delete"),
                        data: {
                            id: id,
                        },
                        success: function (data) {
                            if (data == "success") {
                                load_client(1);
                            } else {
                                $.alert({
                                    title: "تنبيه!",
                                    content: "لا يمكن مسح هذه العميل!",
                                    type: "red",
                                    typeAnimated: true,
                                    rtl: true,
                                });
                            }
                        },
                    });
                },
                كلا: function () {},
            },
        });
    });
    // Jquery Dependency
    $(document).on("keyup", "input[data-type='currency']", function () {
        formatCurrency($(this));
    });
    $(document).on("blur", "input[data-type='currency']", function () {
        formatCurrency($(this));
    });
    function formatNumber(n) {
        // format number 1000000 to 1,234,567
        return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function formatCurrency(input, type = 0) {
        // appends $ to value, validates decimal side
        // and puts cursor back in right position.

        // get input value
        if (type == 1) {
            var input_val = input;
        } else {
            var input_val = input.val();
        }

        // don't validate empty input
        if (input_val === "") {
            return;
        }

        // original length
        var original_len = input_val.length;

        // initial caret position
        var caret_pos = input.prop("selectionStart");

        // check for decimal
        if (input_val.indexOf(".") >= 0) {
            // get position of first decimal
            // this prevents multiple decimals from
            // being entered
            var decimal_pos = input_val.indexOf(".");

            // split number by decimal point
            var left_side = input_val.substring(0, decimal_pos);
            var right_side = input_val.substring(decimal_pos);

            // add commas to left side of number
            left_side = formatNumber(left_side);

            // validate right side
            right_side = formatNumber(right_side);

            // Limit decimal to only 2 digits
            right_side = right_side.substring(0, 2);

            // join number by .
            input_val = "" + left_side + "." + right_side;
        } else {
            // no decimal entered
            // add commas to number
            // remove all non-digits
            input_val = formatNumber(input_val);
            input_val = "" + input_val;
        }

        // send updated string to input
        input.val(input_val);

        // put caret back in the right position
        var updated_len = input_val.length;
        caret_pos = updated_len - original_len + caret_pos;
        input[0].setSelectionRange(caret_pos, caret_pos);
    }
    $("#showPassword").on("click", function () {
        var x = $(".password");
        if (x.attr("type") === "password") {
            x.attr("type", "text");
        } else {
            x.attr("type", "password");
        }
    });
    $(document).on("change", ".changetypesold", function () {
        var html1 =
            '<div class="col-sm-2 append1">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input15">مبلغ البيع</span>' +
            '<input type="text" class="form-control" name="sold_price" aria-describedby="input15" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append1">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input16">قيمة المقدمة</span>' +
            '<input type="text" class="form-control" name="begen_price" aria-describedby="input16" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append1">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input17">تاريخ المقدمة</span>' +
            '<input type="date" class="form-control" name="begen_date" aria-describedby="input17" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append1">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input18">مبلغ تسليم المفتاح</span>' +
            '<input type="text" class="form-control" name="delvery_key_prise" aria-describedby="input18" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append1">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input19">تاريخ تسليم المفتاح</span>' +
            '<input type="date" class="form-control" name="delvery_key_date" aria-describedby="input19" required>' +
            "</div>" +
            "</div>";

        var html2 =
            '<div class="col-sm-2 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input15">مبلغ البيع</span>' +
            '<input type="text" class="form-control" name="sold_price" aria-describedby="input15" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input16">قيمة المقدمة</span>' +
            '<input type="text" class="form-control" name="begen_price" aria-describedby="input16" data-type="currency" required>' +
            " </div>" +
            "</div>" +
            '<div class="col-sm-2 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input177">تاريخ المقدمة</span>' +
            '<input type="date" class="form-control" name="begen_date" aria-describedby="input177" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input17">مبلغ تسليم المفتاح</span>' +
            '<input type="text" class="form-control" name="delvery_key_prise" aria-describedby="input17" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input18">عدد الاشهر</span>' +
            '<select class="form-control" name="months_no" aria-describedby="input18" required>' +
            '<option value="">اختر...</option>';
        for (let i = 1; i <= 240; i++) {
            html2 += '<option value="' + i + '">' + i + "</option>";
        }
        html2 +=
            "</select>" +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input1888">عدد السنوات</span>' +
            '<select class="form-control" name="years_no" aria-describedby="input1888" required>' +
            '<option value="">اختر...</option>';
        for (let i = 1; i <= 20; i++) {
            html2 += '<option value="' + i + '">' + i + "</option>";
        }
        html2 +=
            "</select>" +
            "</div>" +
            "</div>" +
            '<div class="col-sm-4 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input19">قيمة الدفع السنوي</span>' +
            '<input type="text" class="form-control" name="annual_price" aria-describedby="input19" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-4 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input20">تاريخ القسط الاول</span>' +
            '<input type="date" class="form-control" name="first_installment_date" aria-describedby="input20" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input21">تاريخ تسليم المفتاح</span>' +
            '<input type="date" class="form-control" name="delvery_key_date" aria-describedby="input21" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-4 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input22">قيمة الدفع الشهري</span>' +
            '<input type="text" class="form-control" name="monthly_price" readonly aria-describedby="input22" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append2">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input100">المبالغ المضافة</span>' +
            '<select class="form-control" name="addprice" aria-describedby="input100" required>' +
            '<option value="كلا">كلا</option>' +
            '<option value="نعم">نعم</option>' +
            "</select>" +
            "</div>" +
            "</div>" +
            '<div class="appendaddprice append2">' +
            "</div>";
        var html3 =
            '<div class="col-sm-2 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input15">مبلغ البيع</span>' +
            '<input type="text" class="form-control" name="sold_price" aria-describedby="input15" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input16">قيمة المقدمة</span>' +
            '<input type="text" class="form-control" name="begen_price" aria-describedby="input16" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input177">تاريخ المقدمة</span>' +
            '<input type="date" class="form-control" name="begen_date" aria-describedby="input177" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input17">مبلغ تسليم المفتاح</span>' +
            '<input type="text" class="form-control" name="delvery_key_prise" aria-describedby="input17" data-type="currency" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-3 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input18">تاريخ تسليم المفتاح</span>' +
            '<input type="date" class="form-control" name="delvery_key_date" aria-describedby="input18" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-2 append3">' +
            '<div class="input-group mb-3">' +
            '<span class="input-group-text" id="input19">عدد الدفعات</span>' +
            '<select class="form-control input20" name="payments_no" aria-describedby="input19" required>' +
            '<option value="">اختر...</option>';
        for (let i = 1; i <= 20; i++) {
            html3 += '<option value="' + i + '">' + i + "</option>";
        }
        html3 += "</select></div></div>";
        $(".appendinputs").html("");
        $(".appendType").parent().append("");
        $("body .append1").remove();
        $("body .append2").remove();
        $("body .append3").remove();
        if ($(this).val() == "نقداً") {
            $(".appendType").parent().append(html1);
        } else if ($(this).val() == "اقساط") {
            $(".appendType").parent().append(html2);
        } else if ($(this).val() == "دفعات محددة") {
            $(".appendType").parent().append(html3);
        }
    });
    $(document).on("change", "[name='years_no']", function () {
        $("[name='addprice']").prop("selectedIndex", 0);
        $(".appendaddprice").html("");
    });
    $(document).on("change", "[name='addprice']", function () {
        if ($(this).val() == "نعم") {
            var html = "";
            for (var i = 1; i <= $('[name="years_no"]').val(); i++) {
                html +=
                    '<div class="col-sm-3"><div class="input-group mb-3">' +
                    '<span class="input-group-text">قيمة المبلغ المضاف للسنة (' +
                    i +
                    ")</span>" +
                    '<input type="text" class="form-control" value="0" name="prise_add' +
                    i +
                    '" data-type="currency" required>' +
                    "</div></div>";
            }
            $(".appendaddprice").html(html);
            $(".appendaddprice").addClass("row");
        } else {
            $(".appendaddprice").html("");
        }
    });
    $(document).on("change", ".input20", function () {
        var html = "",
            sold_price = $('[name="sold_price"]').val(),
            begen_price = $('[name="begen_price"]').val(),
            delvery_key_prise = $('[name="delvery_key_prise"]').val(),
            res1 =
                Number(begen_price.replace(/\,/g, "")) +
                Number(delvery_key_prise.replace(/\,/g, "")),
            res2 =
                (Number(sold_price.replace(/\,/g, "")) - Number(res1)) /
                $(this).val();

        for (var i = 1; i <= $(this).val(); i++) {
            html +=
                '<div class="row mb-3 "><div class="col-sm-2">' +
                '<div class="input-group mb-3">' +
                '<span class="input-group-text" id="input23">مبلغ الدفعة</span>' +
                '<input type="text" class="form-control" value="' +
                new Intl.NumberFormat().format(Math.floor(res2)) +
                '" name="payment_price[]" aria-describedby="input23" data-type="currency" required>' +
                "</div>" +
                "</div>" +
                '<div class="col-sm-3">' +
                '<div class="input-group mb-3">' +
                '<span class="input-group-text" id="input24">تاريخ تسليم المفتاح</span>' +
                '<input type="date" class="form-control" name="payment_date[]" aria-describedby="input24" required>' +
                "</div>" +
                "</div></div>";
        }
        $(".appendinputs").html(html);
    });

    $("#input5").on("select2:select", function (e) {
        var id = e.params.data.id;
        $.ajax({
            method: "POST",
            url: $("#input5").attr("data-url-fetch"),
            data: {
                id: id,
            },
            success: function (data) {
                $("#input6666").val(data.wallets.wallet_price);

                $(".appendinstallments").html(data.installments);
                $('[name="deposit_type"]').html(
                    '<option value="">اختر...</option>' +
                        '<option value="نقداً">نقداً</option>' +
                        '<option value="صك">صك</option>' +
                        '<option value="اضافة الى المحفظة">اضافة الى المحفظة</option>' +
                        '<option value="سحب من المحفظة">سحب من المحفظة</option>'
                );
            },
            error: function (d) {
                console.log(d);
            },
        });
    });
    $(document).on("change", '[name="price_name"]', function () {
        var res = "";
        var price = $('[name="deposit_amount"]').val();
        var dataGetId = $(this).attr("data-id");
        var price_name = $(this);

        if ($(this).is(":checked")) {
            if ($('[name="deposit_type"]').val() == "سحب من المحفظة") {
                if (
                    Number(price_name.attr("data-price").replace(/\,/g, "")) >
                    Number($("#input6666").val().replace(/\,/g, ""))
                ) {
                    $.alert({
                        title: "تنبيه!",
                        content: "لايوجد مبالغ كافية في المحفظة!",
                        type: "red",
                        typeAnimated: true,
                        rtl: true,
                    });
                    $('[name="deposit_amount"]').val("");
                    $("[name='price_name']").prop("checked", false);
                    return false;
                }
            }
            res =
                Number(price.replace(/\,/g, "")) +
                Number($(this).attr("data-price").replace(/\,/g, ""));

            $("#" + dataGetId).val($(this).attr("data-get-id"));
            $('[name="deposit_amount"]').attr("readonly", "readonly");
        } else {
            res =
                Number(price.replace(/\,/g, "")) -
                Number($(this).attr("data-price").replace(/\,/g, ""));
            $("#" + dataGetId).val("");
            if (res == "0") {
                $('[name="deposit_amount"]').removeAttr("readonly");
            }
        }

        $('[name="deposit_amount"]').val(new Intl.NumberFormat().format(res));
    });
    $(document).on("change", "[name='rule_name']", function () {
        if ($(this).val() == "user1") {
            $(".appendRules").html(
                '<div class="form-group col-md-6">' +
                    '<div class="form-check">' +
                    '<input class="form-check-input" name="is_deposits" type="checkbox" value="1" checked id="defaultCheck1">' +
                    '<label class="form-check-label" for="defaultCheck1">' +
                    " دفتر الايداع" +
                    "</label>" +
                    "</div>" +
                    '<div class="form-check">' +
                    '<input class="form-check-input" name="is_deposits_new" type="checkbox" value="1" checked id="defaultCheck2">' +
                    '<label class="form-check-label" for="defaultCheck2">' +
                    " ايداع جديد" +
                    "</label>" +
                    "</div>" +
                    '<div class="form-check">' +
                    '<input class="form-check-input" name="is_expenses" type="checkbox" value="1" checked id="defaultCheck3">' +
                    '<label class="form-check-label" for="defaultCheck3">' +
                    " دفتر الصرف" +
                    "</label>" +
                    "</div>" +
                    '<div class="form-check">' +
                    '<input class="form-check-input" name="is_expenses_new" type="checkbox" value="1" checked id="defaultCheck4">' +
                    '<label class="form-check-label" for="defaultCheck4">' +
                    "صرف جديد" +
                    "</label>" +
                    "</div>" +
                    "</div>"
            );
        } else {
            $(".appendRules").html("");
        }
    });
    $(document).on(
        "change",
        "[name='months_no'],[name='years_no'], [name='sold_price'], [name='begen_price'], [name='delvery_key_prise'], [name='annual_price'], [name='prise_add1'], [name='prise_add2'], [name='prise_add3'], [name='prise_add4'], [name='prise_add5'], [name='prise_add6'], [name='prise_add7'], [name='prise_add8'], [name='prise_add9'], [name='prise_add10'], [name='prise_add11'], [name='prise_add12'], [name='prise_add13'], [name='prise_add14'], [name='prise_add15'], [name='prise_add16'], [name='prise_add17'], [name='prise_add18'], [name='prise_add19'], [name='prise_add20'], [name='addprice']",
        function () {
            var sold_price = $('[name="sold_price"]').val(),
                begen_price = $('[name="begen_price"]').val(),
                delvery_key_prise = $('[name="delvery_key_prise"]').val(),
                months_no = $('[name="months_no"]').val(),
                years_no = $('[name="years_no"]').val(),
                annual_price = $('[name="annual_price"]').val();

            for (var i = 0; i <= 20; i++) {
                window["prise_add" + i] = $(
                    '[name="prise_add' + i + '"]'
                ).val();
            }

            if ($('[name="addprice"]').val() == "كلا") {
                if (
                    sold_price !== "" &&
                    begen_price !== "" &&
                    delvery_key_prise !== "" &&
                    annual_price !== ""
                ) {
                    var res1 =
                            Number(begen_price.replace(/\,/g, "")) +
                            Number(delvery_key_prise.replace(/\,/g, "")) +
                            Number(annual_price.replace(/\,/g, "")) *
                                Number(years_no),
                        res2 =
                            Number(sold_price.replace(/\,/g, "")) -
                            Number(res1),
                        res3 = Number(res2) / Number(months_no);

                    if (res1 > sold_price.replace(/\,/g, "")) {
                        $.alert({
                            title: "تنبيه!",
                            content: "ادخال المبالغ غير صحيحة!",
                            type: "red",
                            typeAnimated: true,
                            rtl: true,
                        });
                        $('[name="monthly_price"]').val("");
                    } else {
                        formatCurrency(
                            $('[name="monthly_price"]').val(Math.floor(res3))
                        );
                    }
                }
            }
            if ($('[name="addprice"]').val() == "نعم") {
                if (
                    sold_price !== "" &&
                    begen_price !== "" &&
                    delvery_key_prise !== "" &&
                    annual_price !== ""
                ) {
                    console.log(
                        (prise_add8 && prise_add8.length === 0) ||
                            prise_add8 == "0"
                            ? "Good"
                            : "BAD"
                    );

                    var res1 =
                            Number(begen_price.replace(/\,/g, "")) +
                            Number(delvery_key_prise.replace(/\,/g, "")) +
                            Number(
                                prise_add1 ? prise_add1.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add2 ? prise_add2.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add3 ? prise_add3.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add4 ? prise_add4.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add5 ? prise_add5.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add6 ? prise_add6.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add7 ? prise_add7.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add8 ? prise_add8.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add9 ? prise_add9.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add10 ? prise_add10.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add11 ? prise_add11.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add12 ? prise_add12.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add13 ? prise_add13.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add14 ? prise_add14.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add15 ? prise_add15.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add16 ? prise_add16.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add17 ? prise_add17.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add18 ? prise_add18.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add19 ? prise_add19.replace(/\,/g, "") : 0
                            ) +
                            Number(
                                prise_add20 ? prise_add20.replace(/\,/g, "") : 0
                            ) +
                            Number(annual_price.replace(/\,/g, "")) *
                                Number(years_no),
                        res2 =
                            Number(sold_price.replace(/\,/g, "")) -
                            Number(res1),
                        res3 = Number(res2) / Number(months_no);

                    if (res1 > sold_price.replace(/\,/g, "")) {
                        $.alert({
                            title: "تنبيه!",
                            content: "ادخال المبالغ غير صحيحة!",
                            type: "red",
                            typeAnimated: true,
                            rtl: true,
                        });
                        $('[name="monthly_price"]').val("");
                    } else {
                        formatCurrency(
                            $('[name="monthly_price"]').val(Math.floor(res3))
                        );
                    }
                }
            }
        }
    );
    $(".deleteUser").on("click", function () {
        var deleteurl = $(this),
            id = deleteurl.attr("id");
        $.confirm({
            title: "تأكيد!",
            content: "هل انت متأكد؟",
            rtl: true,
            buttons: {
                نعم: function () {
                    $.ajax({
                        method: "POST",
                        url: deleteurl.attr("data-delete-user"),
                        data: {
                            id: id,
                        },
                        success: function () {
                            location.reload();
                        },
                    });
                },
                كلا: function () {},
            },
        });
    });
    $(document).on("click", ".edituser", function () {
        $("#editUser").modal("show");
        $(".getUserId").val($(this).attr("id"));
        $("#fname").val($(this).attr("data-name"));
        $("#uname").val($(this).attr("data-username"));
        $("#urule_name").val($(this).attr("data-rule"));
        $("#mobno").val($(this).attr("data-mobile-number"));
        $("#old_password").val($(this).attr("data-password"));
        $(".appendRules").html("");
        if ($(this).attr("data-rule") == "user1") {
            var html =
                '<div class="form-group col-md-6">' +
                '<div class="form-check">' +
                ' <input class="form-check-input" name="is_deposits" type="checkbox" value="1" ' +
                ($(this).attr("data-is-deposits") == "1" ? "checked" : "") +
                ' id="defaultCheck1">' +
                '<label class="form-check-label" for="defaultCheck1">' +
                "دفتر الايداع" +
                "</label>" +
                "</div>" +
                '<div class="form-check">' +
                '<input class="form-check-input" name="is_deposits_new" type="checkbox"' +
                'value="1" ' +
                ($(this).attr("data-is-deposits_new") == "1" ? "checked" : "") +
                ' id="defaultCheck2">' +
                '<label class="form-check-label" for="defaultCheck2">' +
                "ايداع جديد" +
                "</label>" +
                "</div>" +
                '<div class="form-check">' +
                '<input class="form-check-input" name="is_expenses" type="checkbox" value="1" ' +
                ($(this).attr("data-is-expenses") == "1" ? "checked" : "") +
                ' id="defaultCheck3">' +
                '<label class="form-check-label" for="defaultCheck3">' +
                "دفتر الصرف" +
                "</label>" +
                "</div>" +
                '<div class="form-check">' +
                '<input class="form-check-input" name="is_expenses_new" type="checkbox"' +
                'value="1" ' +
                ($(this).attr("data-is-expenses_new") == "1" ? "checked" : "") +
                ' id="defaultCheck4">' +
                '<label class="form-check-label" for="defaultCheck4">' +
                "صرف جديد" +
                "</label>" +
                "</div>" +
                "</div>";
            $(".appendRules").html(html);
        }
    });

    $(document).on("submit", "#profileUpdate", function (e) {
        e.preventDefault();

        if ($("#pass").val() === $("#rpass").val()) {
            var formData = new FormData(this);
            $.ajax({
                method: "POST",
                url: $("#profileUpdate").attr("data-profile-update"),
                data: formData,
                contentType: false,
                cache: false,
                processData: false,
                beforeSend: function () {
                    $(".updatebtn").html("يتم المعالجة...");
                    $(".updatebtn").attr("disabled", "disabled");
                },
                success: function () {
                    location.reload();
                },
            });
        } else {
            $.alert({
                title: "تنبيه!",
                content: "كلمة السر غير متطابقة!",
                type: "red",
                typeAnimated: true,
                rtl: true,
            });
        }
    });
    // Show logs
    $(document).on("click", ".showlogs", function () {
        var showlogs = $(this),
            id = showlogs.attr("id"),
            type = showlogs.attr("data-logs-type");
        console.log(type);

        $.ajax({
            method: "POST",
            url: showlogs.attr("data-url-logs"),
            data: {
                id: id,
                type: type,
            },
            success: function (data) {
                $(".appendLogs").html(data);
                $("#logsModal").modal("show");
            },
            error: function (d) {
                console.log(d);
            },
        });
    });
});
window.forceReload = function () {
    if (!window.fetch) return document.location.reload(true);
    var els = document.getElementsByTagName("*");
    for (var i = 0; i < els.length; i++) {
        var src = "";
        if (els[i].tagName == "A") continue;
        if (!src && els[i].src) src = els[i].getAttribute("src");
        if (!src && els[i].href) src = els[i].getAttribute("href");
        if (!src) continue;
        fetch(src, { cache: "reload" });
    }
    return document.location.reload(true);
};
document
    .getElementById("bell")
    .animate(
        [
            { transform: "rotate(0deg)" },
            { transform: "rotate(15deg)" },
            { transform: "rotate(-15deg)" },
            { transform: "rotate(10deg)" },
            { transform: "rotate(-10deg)" },
            { transform: "rotate(0deg)", offset: 0.5 },
        ],
        {
            duration: 1500,
            iterations: Infinity,
        }
    );
function printDiv(divName) {
    document.getElementById("hideBtnPrint").style.display = "none";
    var printContents = document.getElementById(divName).innerHTML;
    var originalContents = document.body.innerHTML;

    document.body.innerHTML = printContents;

    window.print();

    document.body.innerHTML = originalContents;
    document.getElementById("hideBtnPrint").style.display = "block";
}
