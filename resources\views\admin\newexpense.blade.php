@extends('admin.layouts.app')

@section('title', 'اضافة صرف جديد')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">
                    <h4 style="font-weight: 700;font-size: 17px;">اضافة صرف جديد</h4>
                    @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
                <div class="card-body">
                    <form class="newExpense" data-form-expense-url="{{route('expense.store')}}" method="POST"
                        enctype="multipart/form-data">
                        @csrf
                        <label class="form-label" for="input1">تاريخ الصرف</label>
                        <div style="display: flex;align-items: center;">
                            <div class="me-5 ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input1">اليوم</label>
                                    <select class="form-select" id="input1" style="width: 95px;" name="day" required>
                                        @for ($i = 1; $i <= 31; $i++) <option value="{{$i}}" {{date("d")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="me-5 ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input2">الشهر</label>
                                    <select class="form-select" id="input2" style="width: 95px;" name="month" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 1; $i <= 12; $i++) <option value="{{$i}}" {{date("m")==$i
                                            ? 'selected' : '' }}>{{$i}}</option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="input-group mb-3">
                                    <label class="input-group-text" for="input3">السنة</label>
                                    <select class="form-select" id="input3" style="width: 150px;" name="year" required>
                                        <option value="">اختر...</option>
                                        @for ($i = 2023; $i <= date("Y"); $i++) <option value="{{$i}}" {{date("Y")==$i
                                            ? 'selected' : '' }}>{{$i}}
                                            </option>
                                            @endfor
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <div class="col-4">
                                <label class="form-label" for="input4">باب الصرف</label>
                                <select class="form-select" id="input4" data-type-door="2" name="expense_door" required>
                                </select>
                            </div>
                            <div class="col-5 hiddenInput" style="display: none">
                                <label for="input55" class="form-label">اسم الصرف</label>
                                <input class="form-control" id="input55" name="expense_name">
                            </div>
                            <div class="col-3 hiddenInput" style="display: none">
                                <label for="input77" class="form-label">مبلغ الصرف</label>
                                <input class="form-control text-dark" style="font-weight:bold" type="text" id="input77"
                                    name="expense_amount" data-type="currency" required>
                            </div>
                        </div>
                        <div class="form-group mt-3 row">
                            <label for="input8" class="form-label">التفاصيل</label>
                            <input class="form-control" type="text" id="input8" name="expense_details" required>
                        </div>
                        <div class="d-flex justify-content-between align-items-baseline">
                            <div class="form-group mt-30 " style="width: 60px;">
                                <label for="input9" class="btn btn-custom filupp">
                                    <i class="fa-solid fa-upload"></i>
                                    <input type="file" multiple name="expense_file[]" class="d-none" value="1"
                                        id="input9" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xlsx" />
                                </label>
                            </div>
                            <div>
                                <span class="filupp-file-name js-value ms-5"></span>
                            </div>
                            <div class="form-group mt-3 text-end">
                                <button type="submit" class="btn btn-success btn-custom btn-send">اضافة</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@include('admin.include.blog1')
@endsection