@extends('admin.layouts.app')

@section('title', 'المستخدمين')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">

                </div>
                <div class="card" data-aos="fade-up" data-aos-delay="800">
                    <div class="card-body">
                        <div id="d-main" class="d-main">
                            <table id="basic-table" class="table table-striped mb-0" role="grid">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>اسم المستخدم</th>
                                        <th>رقم الهاتف</th>
                                        <th>الصلاحية</th>
                                        @if (\Auth::user()->rule != 'user4')
                                        <th>الادارة</th>
                                        @endif
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($users as $user)
                                    <tr>
                                        <td>{{$user->id}}</td>
                                        <td>{{$user->name}}</td>
                                        <td>{{$user->username}}</td>
                                        <td>{{$user->mobile_number}}</td>
                                        <td>{{$user->rule_name}}</td>
                                        @if (\Auth::user()->rule != 'user4')
                                        <td>
                                            <a type="submit" class="btn btn-primary btn-sm edituser" id="{{$user->id}}"
                                                title="تعديل" data-name="{{$user->name}}"
                                                data-username="{{$user->username}}" data-rule="{{$user->rule}}"
                                                data-is-deposits="{{$user->is_deposits}}"
                                                data-is-deposits_new="{{$user->is_deposits_new}}"
                                                data-is-expenses="{{$user->is_expenses}}"
                                                data-is-expenses_new="{{$user->is_expenses_new}}"
                                                data-mobile-number="{{$user->mobile_number}}"
                                                data-password="{{$user->password}}"><i class="fa-solid fa-edit"></i></a>
                                            <button type="submit" class="btn btn-danger btn-sm deleteUser"
                                                id="{{$user->id}}" data-delete-user="{{route('admin.users.delete')}}"
                                                title="حذف"><i class="fa-solid fa-trash"></i></button>
                                        </td>
                                        @endif
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <br>
                            {{$users->links();}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('admin.include.blog1')
@endsection