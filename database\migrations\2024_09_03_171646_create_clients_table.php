<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('house_no')->nullable();
            $table->string('house_space')->nullable();
            $table->string('buld_space')->nullable();
            $table->string('rooms_no')->nullable();
            $table->string('totol_house_price')->nullable();
            $table->string('name_purshes')->nullable();
            $table->string('name_mother_purshes')->nullable();
            $table->string('phone1')->nullable();
            $table->string('phone2')->nullable();
            $table->string('id_no')->nullable();
            $table->string('id_relese')->nullable();
            $table->string('id_relese_date')->nullable();
            $table->string('address')->nullable();
            $table->string('sold_type')->nullable();
            $table->string('sold_price')->nullable();
            $table->string('begen_price')->nullable();
            $table->string('begen_date')->nullable();
            $table->string('delvery_key_prise')->nullable();
            $table->string('delvery_key_date')->nullable();
            $table->string('months_no')->nullable();
            $table->string('monthly_price')->nullable();
            $table->string('first_installment_date')->nullable();
            $table->string('payments_no')->nullable();
            $table->string('payment_price')->nullable();
            $table->string('payment_date')->nullable();
            $table->string('files')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('isDelete')->nullable()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clients');
    }
};
