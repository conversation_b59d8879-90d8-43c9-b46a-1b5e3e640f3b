@extends('admin.layouts.app')

@section('title', 'مجمع النجوم السكني')
@section('content')
<div class="col-md-12 col-lg-8">
    <div class="row">
        <!--<div class="col-md-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="flex-wrap card-header d-flex justify-content-between align-items-center">

                </div>
                <div class="card" data-aos="fade-up" data-aos-delay="800">
                    <div class="flex-wrap card-header d-flex justify-content-between align-items-center">
                        <div class="header-title">
                            <h6 class="card-title">المخطط البياني للايداعات والصرفيات</h6>
                        </div>
                        <div class="d-flex align-items-center justify-content-between align-self-center">

                            <div class="d-flex align-items-center text-primary">
                                <svg class="icon-12" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24"
                                    fill="currentColor">
                                    <g>
                                        <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                    </g>
                                </svg>
                                <div class="ms-2">
                                    <span class="text-gray">الايداع</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center ms-3 text-info">
                                <svg class="icon-12" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24"
                                    fill="currentColor">
                                    <g>
                                        <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                    </g>
                                </svg>
                                <div class="ms-2">
                                    <span class="text-gray">الصرف</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="d-main" class="d-main"></div>
                    </div>
                </div>
            </div>
        </div> -->

        <div class="col-md-12 col-lg-12" id="tablelateclient2">
            <div class="overflow-hidden card" data-aos="fade-up" data-aos-delay="600">
                <div class="flex-wrap card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h6 class="mb-2 card-title">اقساط العملاء المستحقين والمتأخرة بالتسديد</h6>
                    </div>
                    <div class="header-title" id="hideBtnPrint">
                        <button type="button" class="btn btn-primary btn-sm mb-2" onclick="printDiv('tablelateclient2')"
                            title="طباعة"><i class="fa-solid fa-print"></i></button>
                    </div>
                </div>
                <div class="p-0 card-body" id="tablelateclient">
                    <div class="mt-4 table-responsive">
                        <table id="basic-table" class="table mb-0 table-striped" role="grid">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم العميل</th>
                                    <th>رقم الدار</th>
                                    <th>المبلغ</th>
                                    <th>نوع القسط</th>
                                    <th>تاريخ التسديد</th>
                                    <th>فترة التأخير</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $clients = Illuminate\Support\Facades\DB::table('clients')
                                            ->join('installments', 'clients.id', '=', 'installments.client_id')
                                            ->where('price_date', '<=', date("Y-m-d"))->where('status', '0')
                                            ->select('clients.*', 'installments.*')
                                            ->orderBy('installments.price_date', 'desc')
                                            ->get();
                                            $sum = 0;
                                foreach ($clients as $key => $item) {
                                    $sum += str_replace(',', '', $item->price);
                                    $fdate = date("Y-m-d", strtotime($item->price_date));
                                    $tdate = date("Y-m-d");
                                    $datetime1 = new DateTime($fdate);
                                    $datetime2 = new DateTime($tdate);
                                    $interval = $datetime1->diff($datetime2);
                                    $days = $interval->format('%a');
                                    ?>
                                <tr target="_blank"
                                    onclick="window.open('{{route('clients.view', $item->client_id)}}','_blank')"
                                    style="cursor: pointer">
                                    <td>{{$key + 1}}</td>
                                    <td>{{$item->name_purshes}}</td>
                                    <td>{{$item->house_no}}</td>
                                    <td>{{$item->price}}</td>
                                    <td>{{$item->price_name}}</td>
                                    <td>{{date("Y-m-d", strtotime($item->price_date))}}</td>
                                    <td>{{$days == '0' ? 'مستحق الدفع اليوم' : $days . ' يوم'}}</td>
                                </tr>

                                <?php
                                    }
                                    ?>
                                <tr class="table-primary">
                                    <td colspan="3">المجموع</td>
                                    <td colspan="6">{{number_format($sum, 0)}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('admin.include.blog1')
@endsection