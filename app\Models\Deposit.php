<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Deposit extends Model
{
    use HasFactory;

    protected $fillable = [
        'day',
        'month',
        'year',
        'deposit_date',
        'deposit_door',
        'deposit_door_name',
        'deposit_name',
        'client_name',
        'house_no',
        'deposit_type',
        'deposit_amount',
        'price_name',
        'deposit_details',
        'deposit_file',
        'user_id',
        'installments_id',
        'wallet',
        'isDelete',
    ];
}
