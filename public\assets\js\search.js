function load_data(
    page,
    query = "",
    query2 = "",
    query3 = "",
    query4 = "",
    query5 = ""
) {
    $.ajax({
        url: $("#search_form").attr("data-url-show") + "?page=" + page,
        method: "GET",
        dataType: "json",
        data: {
            page: page,
            query: query,
            query2: query2,
            query3: query3,
            query4: query4,
            query5: query5,
        },
        success: function (data) {
            $("#dynamic_table").html(data.table_data);
            $("#sumAmount").text(data.sum);
        },
        error: function (data) {
            console.log(data);
        },
    });
}
$(function () {
    "use strict";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    // Search Ajax
    load_data(1);

    $(document).on("click", ".pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1],
            query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val(),
            query5 = $("#search5").val();
        load_data(page, query, query2, query3, query4, query5);
    });

    $("#search_form").bind("keyup change", function () {
        var query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val(),
            query5 = $("#search5").val();

        load_data(1, query, query2, query3, query4, query5);
    });
});
