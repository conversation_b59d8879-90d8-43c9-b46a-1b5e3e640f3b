function load_client(page, query = "", query2 = "", query3 = "", query4 = "") {
    $.ajax({
        url: $("#search_form2").attr("data-url-show-client") + "?page=" + page,
        method: "GET",
        dataType: "json",
        data: {
            page: page,
            query: query,
            query2: query2,
            query3: query3,
            query4: query4,
        },
        success: function (data) {
            $("#dynamic_table").html(data.table_data);
        },
        error: function (data) {
            console.log(data);
        },
    });
}
$(function () {
    "use strict";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    // Search Ajax
    load_client(1);

    $(document).on("click", ".pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1],
            query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val();
        load_client(page, query, query2, query3, query4);
    });

    $("#search_form2").bind("keyup change", function () {
        var query = $("#search").val(),
            query2 = $("#search2").val(),
            query3 = $("#search3").val(),
            query4 = $("#search4").val();

        load_client(1, query, query2, query3, query4);
    });
});
